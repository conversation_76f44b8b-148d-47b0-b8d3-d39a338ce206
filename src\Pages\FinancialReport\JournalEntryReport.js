import React, { useEffect, useState } from "react";
import { Col, Row, Modal, Button, FormControl, Form } from "react-bootstrap"; 
import { DatePicker } from "antd";
import { useNavigate, useLocation } from "react-router-dom";
import Navbar from "../../Components/Navbar/Navbar";
import { formatAmount, firstDayOfMonth, lastDayOfMonth, formatDateNoTime, numberFormat, refreshPage } from "../../Helpers/Utils/Common";
import { getJournalEntryReport, deleteJournalEntry, postJournalEntry, getProjects } from "../../Helpers/apiCalls/SalesReport/SalesReportApi";
import Table from "../../Components/TableTemplate/Table";
import { toast } from "react-toastify";
import ReactLoading from "react-loading";

const { RangePicker } = DatePicker;

export default function JournalEntry() {
  const [inactive, setInactive] = useState(true);
  const [filterConfig, setFilterConfig] = useState({
    date_from: null,
    date_to: null,
  });
  const [showLoader, setShowLoader] = useState(false);
  const [tableData, setTableData] = useState([]);
  const [selectedEntry, setSelectedEntry] = useState(null); 
  const [showModal, setShowModal] = useState(false); 
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isPosting, setIsPosting] = useState(false);
  const [projects, setProjects] = useState({});
  const navigate = useNavigate();
  const location = useLocation();

  // Add this new function to fetch projects
  const fetchProjects = async () => {
    const response = await getProjects();
    if (response.data?.data) {
      const projectMap = response.data.data.reduce((acc, project) => {
        acc[project.id] = project.name;
        return acc;
      }, {});
      setProjects(projectMap);
    }
  };

  async function fetchTableData() {
    setShowLoader(true);
    setTableData([]);

    const response = await getJournalEntryReport(filterConfig);

    if (response?.data?.data) {
      const formattedData = response.data.data.map((entry, index) => ({
        key: index,
        name: entry.project_name || "N/A",
        date: entry.date ? formatDateNoTime(entry.date) : " ",
        ref_no: entry.id || " ",
        remarks: entry.remarks || " ",
        status: entry.is_posted === "1" ? "Approved" : "Pending",
        total_debit: numberFormat(parseFloat(entry.total_debit || 0).toFixed(2)),
        total_credit: numberFormat(parseFloat(entry.total_credit || 0).toFixed(2)),
        total_amount: parseFloat(entry.total_debit || 0) > 0 
          ? numberFormat(parseFloat(entry.total_debit || 0).toFixed(2))
          : numberFormat(parseFloat(entry.total_credit || 0).toFixed(2))
      }));

      setTableData(formattedData);
    }

    setShowLoader(false);
  }

  // Update useEffect to include filterConfig dependency
  useEffect(() => {
    fetchTableData();
  }, [filterConfig, location]);

  useEffect(() => {
    if (location?.state?.newEntry) {
      const journalEntries = location?.state?.newEntry?.journalEntries || []; // Ensure it's an array
      
      const newEntry = journalEntries.map((entry, index) => ({
        key: tableData?.length + index,
        date: location?.state?.newEntry?.date,
        ref_no: `NEW-${tableData?.length + index + 1}`,
        remarks: entry?.remarks || " ",
        status: "Pending",
        total_debit: numberFormat(parseFloat(entry?.debit || 0).toFixed(2)),
        total_credit: numberFormat(parseFloat(entry?.credit || 0).toFixed(2)),
        added_by_name: "Current User",
      }));
  
      setTableData((prev) => [...prev, ...newEntry]);
      navigate(location?.pathname, { replace: true });
    }
  }, [location?.state]);
  
  // Add projects fetch to initial load
  useEffect(() => {
    fetchProjects();
  }, []);

  // Single handleAction implementation
  const handleAction = (action, id) => {
    if (action === "add") {
      navigate("/journalentry/add");
    } else if (action === "view") {
      navigate(`/journalentry/view/${id}`);
    } else if (action === "edit") {
      navigate(`/journalentry/edit/${id}`);
    }
  };

  // Handle row click to show modal
  const handleRowClick = (record) => {
    setSelectedEntry(record);
    setShowModal(true);
  };

  // Action button for each row
  const ActionBtn = (row) => (
    <Form.Select
      name="action"
      className="PO-select-action"
      onChange={(e) => {
        const action = e.target.value;
        if (action === "view") {
          handleAction("view", row.ref_no);
        } else if (action === "edit") {
          handleAction("edit", row.ref_no);
        }
      }}
    >
      <option value="" hidden>
        Select
      </option>
      <option value="view">View</option>
      <option value="edit" disabled={row.status === "Approved"}>Edit</option>
    </Form.Select>
  );

  const handleDelete = async () => {
    try {
      const response = await deleteJournalEntry(selectedEntry.ref_no);
      
      if (response.data?.status === "success") {
        toast.success("Journal entry deleted successfully");
        setShowModal(false);
        setShowDeleteModal(false);
        setTableData(prev => prev.filter(item => item.ref_no !== selectedEntry.ref_no));
      } else {
        toast.error(response.error?.data?.response || "Failed to delete journal entry");
      }
    } catch (error) {
      console.error("Error deleting journal entry:", error);
      toast.error("Error deleting journal entry");
    }
  };

  const handlePost = async () => {
     
      setIsPosting(true);
      const response = await postJournalEntry(selectedEntry.ref_no, 1);
      
      if (response.data?.response) {
        // First update the entry in the current table data
        const updatedTableData = tableData.map(item => 
          item.ref_no === selectedEntry.ref_no 
            ? {...item, status: "Approved"}
            : item
        );
        setTableData(updatedTableData);
        
        // Update the selected entry
        setSelectedEntry(prev => ({
          ...prev,
          status: "Approved"
        }));
        
        toast.success("Journal entry posted successfully");
        setShowModal(false);
        setIsPosting(true);
        setTimeout(() => {  
        // refreshPage();
        fetchTableData();
        }, 1000);
      } else {
        setIsPosting(false);
        toast.error(response.error?.data?.response || "Failed to post journal entry");
      }
  };

  // Update the useEffect for handling state updates
  useEffect(() => {
    if (location?.state?.refresh || location?.state?.updatedEntry || location?.state?.newEntry) {
      fetchTableData();
      // Clear the state
      navigate(location.pathname, { replace: true });
    }
  }, [location.state]);

  return (
    <div>
      <div className="page">
      <Navbar
        onCollapse={(inactive) => {
          setInactive(inactive);
        }}
        active={"FINANCIAL REPORT"}
      />
      </div>
      <div className={`manager-container ${inactive ? "inactive" : "active"}`}>
        <Row className="mb-4 content-end">
          <Col xs={6}>
            <h1 className="page-title">Journal Entry Report</h1>
          </Col>
          <Col xs={6} className="d-flex justify-content-end">
            <button
              className="add-btn"
              onClick={() => handleAction("add")}
            >
              Add
            </button>
          </Col>
        </Row>

        <div className="tab-content">
          <div className="ms-3 PO-filters PI-filters d-flex mb-4">
            <RangePicker
              placeholder={[
                filterConfig.date_from ?? firstDayOfMonth(),
                filterConfig.date_to ?? lastDayOfMonth(),
              ]}
              onChange={(e) => {
                if (e) {
                  setFilterConfig((prev) => ({
                    ...prev,
                    date_from: e[0].format("YYYY-MM-DD"),
                    date_to: e[1].format("YYYY-MM-DD"),
                  }));
                } else {
                  setFilterConfig((prev) => ({
                    ...prev,
                    date_from: firstDayOfMonth(),
                    date_to: lastDayOfMonth(),
                  }));
                }
              }}
            />
          </div>
          <Table
            tableHeaders={[
              "DATE",
              "REFERENCE No.",
              "STATUS",
              "REMARKS",
              "AMOUNT",
            ]}
            headerSelector={[
              "date",
              "ref_no",
              "status",
              "remarks",
              "total_amount", // Changed from total_debit to total_amount
            ]}
            tableData={tableData}
            showLoader={showLoader}
            tableType="journal_entry"
            onRowClicked={(record) => handleRowClick(record)}
            ActionBtn={(row) => ActionBtn(row)} // Add ActionBtn for each row
          />
        </div>
      </div>

      {/* Modal for viewing entry details */}
      <Modal
        show={showModal}
        onHide={() => setShowModal(false)}
        centered
        dialogClassName="custom-modal-width"
      >
        <Modal.Header closeButton>
          <Modal.Title className="page-title mb-2 w-100 text-left">
            REVIEW JOURNAL ENTRY
          </Modal.Title>
        </Modal.Header>

        <Modal.Body>
          {selectedEntry && (
            <div className="px-4">
              <div className="d-flex justify-content-between gap-3 mb-4">
                <div className="flex-fill">
                  <label className="edit-label mb-1">Date</label>
                  <input
                    type="text"
                    className="nc-modal-custom-input form-control"
                    value={selectedEntry.date}
                    disabled
                  />
                </div>
                <div className="flex-fill">
                  <label className="edit-label mb-1">Reference No.</label>
                  <input
                    type="text"
                    className="nc-modal-custom-input form-control"
                    value={selectedEntry.ref_no}
                    disabled
                  />
                </div>
                <div className="flex-fill">
                  <label className="edit-label mb-1">Amount</label>
                  <input
                    type="text"
                    className="nc-modal-custom-input form-control"
                    value={selectedEntry.total_debit}
                    disabled
                  />
                </div>
              </div>
            </div>
          )}
        </Modal.Body>

        <Modal.Footer>
          <div className="d-flex justify-content-between flex-wrap gap-2 px-4">
            <button
              type="button"
              className="button-tertiary mr-3"
              onClick={() => handleAction("view", selectedEntry?.ref_no)}
            >
              View
            </button>
            <button
              type="button"
              className="button-secondary"
              onClick={() => setShowModal(false)}
            >
              Close
            </button>
            <button
              type="button"
              className="button-warning-fill"
              onClick={() => setShowDeleteModal(true)}
            >
              Delete
            </button>
            <button
              type="button"
              className="button-primary"
              onClick={() => handleAction("edit", selectedEntry?.ref_no)}
              disabled={selectedEntry?.status === "Approved"}
            >
              Edit
            </button>
            <button
              type="button"
              className="button-primary position-relative"
              onClick={handlePost}
              disabled={selectedEntry?.status === "Approved" || isPosting}
            >
              {isPosting ? (
                <div className="d-flex align-items-center justify-content-center gap-2">
                  <ReactLoading
                    type="spinningBubbles"
                    color="#FFF"
                    height={20}
                    width={20}
                    refreshPage
                  />
                  <span>Posting...</span>
                </div>
              ) : 
                "Post"
              }
            </button>
          </div>
        </Modal.Footer>
      </Modal>

      {/* Add delete confirmation modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title className="page-title mb-2 w-100 text-left color-red">DELETE JOURNAL ENTRY</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="d-flex flex-column justify-content-center align-items-center">
            <span className="text-center">
              Are you sure you want to delete this journal entry?
            </span>
          </div>
        </Modal.Body>
        <Modal.Footer className="d-flex justify-content-center">
          <button
            type="button"
            className="button-secondary"
            onClick={() => setShowDeleteModal(false)}
          >
            Cancel
          </button>
          <button
            type="button"
            className="button-warning"
            onClick={handleDelete}
          >
            Proceed
          </button>
        </Modal.Footer>
      </Modal>
    </div>
  );
}
