import React, { useEffect, useState } from "react"
import { Col, Row, Modal, Button } from "react-bootstrap" // Import Modal and Button
import Select from "react-select"
import { DatePicker, Table } from "antd"
import { useNavigate, useLocation } from "react-router-dom"
import Navbar from "../../Components/Navbar/Navbar"
import {
  firstDayOfMonth,
  lastDayOfMonth,
  formatDateNoTime,
  numberFormat,
  selectDropdownStyle,
  formatNum,
  formatAmount,
} from "../../Helpers/Utils/Common"
import { getBankReconciliation } from "../../Helpers/apiCalls/SalesReport/SalesReportApi"
// import Table from "../../Components/TableTemplate/Table"
import { searchBank } from "../../Helpers/apiCalls/Manage/Banks"
import dayjs from "dayjs"

const { RangePicker } = DatePicker

export default function BankReconciliation() {
  const [banksData, setBanksData] = useState([])
  const [inactive, setInactive] = useState(true)
  const [filterConfig, setFilterConfig] = useState({
    date_from: firstDayOfMonth(),
    date_to: lastDayOfMonth(),
    bank_id: "1",
  })
  const [showLoader, setShowLoader] = useState(false)
  const [tableData, setTableData] = useState([])
  const [selectedEntry, setSelectedEntry] = useState(null) // State for selected entry
  const [showModal, setShowModal] = useState(false) // State for modal visibility
  const location = useLocation()

  var columns = [
    {
      title: "Date",
      dataIndex: "date",
      key: "date",
      width: 150,
    },
    {
      title: "Transaction",
      dataIndex: "ref_no",
      key: "ref_no",
      render: (text, record) => (
        <span
          className={`${
            record.ref_no === "Opening" || record.ref_no === "Closing"
              ? "bold"
              : ""
          }`}
        >
          {text}
        </span>
      ),
    },
    {
      title: "Debit",
      dataIndex: "debit",
      key: "debit",
      align: "right",
      render: (text, record) => (
        <span
          className={`${
            record.ref_no === "Opening" || record.ref_no === "Closing"
              ? "bold"
              : ""
          }`}
        >
          {text}
        </span>
      ),
    },
    {
      title: "Credit",
      dataIndex: "credit",
      key: "credit",
      align: "right",
      render: (text, record) => (
        <span
          className={`${
            record.ref_no === "Opening" || record.ref_no === "Closing"
              ? "bold"
              : ""
          }`}
        >
          {text}
        </span>
      ),
    },
    {
      title: "Balance",
      dataIndex: "balance",
      key: "balance",
      align: "right",
      render: (text, record) => (
        <span
          className={`${
            record.ref_no === "Opening" || record.ref_no === "Closing"
              ? "bold"
              : ""
          }`}
        >
          {text}
        </span>
      ),
    },
  ]

  async function fetchTableData() {
    setShowLoader(true)
    setTableData([])

    const response = await getBankReconciliation(filterConfig)

    if (response?.data) {
      var data = response?.data?.bank_reconciliation
      var summary = response.data.bank_summary
      const formattedData = [
        {
          date: "",
          ref_no: "Opening",
          debit: "0.00",
          credit: "0.00",
          balance: summary.previous_balance === "0.00" ? "0.00" : formatAmount(parseFloat(summary.previous_balance)),
        },
      ]

      data?.forEach((entry, index) => {
        formattedData.push({
          key: index,
          date: entry.date ? formatDateNoTime(entry.date) : " ",
          ref_no: entry.reference_no,
          debit:
            entry.type === "Debit"
              ? formatAmount(parseFloat(entry.paid_amount))
              : "0.00",
          credit:
            entry.type === "Credit"
              ? formatAmount(parseFloat(entry.paid_amount))
              : "0.00",
          balance: formatAmount(entry.balance),
        })
      })

      formattedData.push({
        date: "",
        ref_no: "Closing",
        debit: summary.total_debit === 0 ? "0.00" : formatAmount(summary.total_debit),
        credit: summary.total_credit === 0 ? "0.00" : formatAmount(summary.total_credit),
        balance: summary.total_balance === "0.00" ? "0.00" : formatAmount(parseFloat(summary.total_balance)),
      })

      setTableData(formattedData)
    }

    setShowLoader(false)
  }

  async function fetchBanks() {
    setBanksData([])
    setShowLoader(true)
    const response = await searchBank()

    if (response) {
      let result = response.data.data.data.map((bank) => {
        return {
          label: bank.name,
          value: bank.id,
        }
      })
      setBanksData(result)
    }
  }

  useEffect(() => {
    fetchTableData()
  }, [filterConfig, location])

  useEffect(() => {
    fetchBanks()
  }, [])

  // Handle row click to show modal
  const handleRowClick = (record) => {
    setSelectedEntry(record)
    setShowModal(true)
  }

  return (
    <div>
      <div className="page">
        <Navbar
          onCollapse={(inactive) => setInactive(inactive)}
          active="FINANCIAL REPORT"
        />
      </div>
      <div className={`manager-container ${inactive ? "inactive" : "active"}`}>
        <Row className="mb-4">
          <Col xs={6}>
            <h1 className="page-title">Bank Reconciliation</h1>
          </Col>
        </Row>

        <div className="tab-content">
          <div className="ms-3 PO-filters PI-filters d-flex mb-4 gap-3">
            <Select
              options={banksData}
              className="dropsearch-filter px-0 py-0 me-2"
              classNamePrefix="react-select"
              styles={selectDropdownStyle}
              placeholder="Select Bank"
              value={banksData.find(
                (bank) => bank.value === filterConfig.bank_id
              )}
              onChange={(e) =>
                setFilterConfig((prev) => ({
                  ...prev,
                  bank_id: e.value,
                }))
              }
            />
            <RangePicker
              placeholder={[
                filterConfig.date_from ?? firstDayOfMonth(),
                filterConfig.date_to ?? lastDayOfMonth(),
              ]}
              onChange={(e) => {
                if (e) {
                  setFilterConfig((prev) => ({
                    ...prev,
                    date_from: e[0].format("YYYY-MM-DD"),
                    date_to: e[1].format("YYYY-MM-DD"),
                  }))
                } else {
                  setFilterConfig((prev) => ({
                    ...prev,
                    date_from: firstDayOfMonth(),
                    date_to: lastDayOfMonth(),
                  }))
                }
              }}
            />
          </div>
          <div className="px-3 ant-table-container">
            <Table
              pagination={false}
              columns={columns}
              dataSource={tableData}
              showLoader={showLoader}
              onRow={(record) => ({
                onClick: () => {}, // Make rows clickable
              })}
            />
          </div>
        </div>
      </div>

      {/* Modal for viewing entry details */}
      <Modal show={showModal} onHide={() => setShowModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>REVIEW JOURNAL ENTRY</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedEntry && (
            <div>
              <Row className="mb-3">
                <Col xs={4}>
                  <strong>Date:</strong>
                </Col>
                <Col xs={8}>{selectedEntry.date}</Col>
              </Row>
              <Row className="mb-3">
                <Col xs={4}>
                  <strong>Reference No:</strong>
                </Col>
                <Col xs={8}>{selectedEntry.ref_no}</Col>
              </Row>
              <Row className="mb-3">
                <Col xs={4}>
                  <strong>Amount:</strong>
                </Col>
                <Col xs={8}>{selectedEntry.total_debit}</Col>
              </Row>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Cancel
          </Button>
          <Button variant="danger">Delete</Button>
          <Button variant="primary">Edit</Button>
          <Button variant="success">Post</Button>
        </Modal.Footer>
      </Modal>
    </div>
  )
}
