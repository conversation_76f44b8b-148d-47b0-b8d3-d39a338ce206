import { getToken, getToken2, getUser } from "../../Utils/Common";
import { getAPICall, postAPICall } from "../axiosMethodCalls";
import Moment from "moment";

//GET
export const getAllSuppliesExpenses = async () => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "Supplies_expenses/get_all_supplies_expense",
      {
        requester: getUser(),
        token: getToken(),
      }
    );
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};
//GET
export const getAllSuppliesExpensesPotato = async () => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_POTATO +
        "Supplies_expenses/get_all_supplies_expense",
      {
        requester: getUser(),
        token: getToken2(),
      },
      "potato"
    );
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};

// GET SINGLE
export const getSingleSuppliesExpense = async (id) => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "Supplies_expenses/get_supplies_expense",
      {
        requester: getUser(),
        token: getToken(),
        supplies_expense_id: id,
        get_due_date: true // Add this flag to ensure due_date is returned
      }
    );
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};

// GET ALL BRANCHES
export const getAllProjects = async () => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "projects/get_all_projects",
      {
        requester: getUser(),
        token: getToken(),
      }
    );
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};

// DELETE
export const deleteSuppliesExpense = async (id) => {
  try {
    var payload = {
      requester: getUser(),
      token: getToken(),
      supplies_expense_id: id,
    };
    const response = await postAPICall(
      process.env.REACT_APP_LINK + "Supplies_expenses/delete",
      payload
    );

    return { response: response.data };
  } catch (error) {
    return { error: error.response };
  }
};

// CREATE
export const createSuppliesExpense = async (data, items, files) => {
  try {
    const formData = new FormData();
    
    // Handle file attachments if any
    if (files && files.length > 0) {
      files.forEach(file => {
        formData.append('file', file, file.name);
      });
    }

    // Append basic form data
    formData.append('requester', getUser());
    formData.append('token', getToken());
    formData.append('requisitioner', data.requisitioner || "");
    formData.append('supplier_id', data.supplier_id || "");
    formData.append('type', data.type || "");
    formData.append('forwarder_id', data.forwarder_id || "");
    formData.append('expense_type_id', data.expense_type_id || "");
    formData.append('supplies_expense_date', data.supplies_expense_date || "");
    formData.append('delivery_date', data.delivery_date || "");
    formData.append('due_date', data.due_date || "");
    formData.append('remarks', data.remarks || "");
    formData.append('grand_total', data.grand_total || 0);
    formData.append('is_save', data.is_save || "");
    formData.append('payment_method', data.payment_method || "");

    // Append items data
    if (items && items.length > 0) {
      items.forEach((item, index) => {
        formData.append(`names[]`, item.name || "");
        formData.append(`quantities[]`, item.qty || "");
        formData.append(`units[]`, item.unit || "");
        formData.append(`prices[]`, item.price || "");
        formData.append(`item_remarks[]`, item.remarks || "");
      });
    }

    const response = await postAPICall(
      process.env.REACT_APP_LINK + "Supplies_expenses/create",
      formData,
      true // Set isFormData flag to true
    );
    return { status: response.status, data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};

// UPDATE
export const editSuppliesExpense = async (data, items, files, id) => {
  try {
    const formData = new FormData();
    
    // Handle file attachments if any
    if (files && files.length > 0) {
      files.forEach(file => {
        // If the file is already uploaded (has base64), skip it
        if (!file.base64) {
          formData.append('file', file.originFileObj || file);
        }
      });
    }

    // Append basic form data
    formData.append('requester', getUser());
    formData.append('token', getToken());
    formData.append('supplies_expense_id', id);
    formData.append('requisitioner', data.requisitioner || "");
    formData.append('supplier_id', data.supplier_id || "");
    formData.append('type', data.type || "");
    formData.append('forwarder_id', data.forwarder_id || "");
    formData.append('expense_type_id', data.expense_type_id || "");
    formData.append('supplies_expense_date', data.supplies_expense_date || "");
    formData.append('delivery_date', data.delivery_date || "");
    formData.append('due_date', data.due_date || "");
    formData.append('remarks', data.remarks || "");
    formData.append('grand_total', data.grand_total || 0);
    formData.append('is_save', data.is_save || "");
    formData.append('payment_method', data.payment_method || "");

    // Append items data
    if (items && items.length > 0) {
      items.forEach((item) => {
        formData.append(`names[]`, item.name || "");
        formData.append(`quantities[]`, item.qty || "");
        formData.append(`units[]`, item.unit || "");
        formData.append(`prices[]`, item.price || "");
        formData.append(`item_remarks[]`, item.remarks || "");
      });
    }

    const response = await postAPICall(
      process.env.REACT_APP_LINK + "Supplies_expenses/update",
      formData,
      true
    );
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};

export const emailSE = async (id) => {
  try {
    const response = await postAPICall(
      process.env.REACT_APP_LINK + "Supplies_expenses/send_email_to_supplier",
      {
        requester: getUser(),
        token: getToken(),
        supplies_expense_id: id,
      }
    );
    return { response: response.data };
  } catch (error) {
    return { error: error.response };
  }
};

export const receiveSE = async (SE) => {
  const userDetails = {
    requester: getUser(),
    token: getToken(),
  };
  const payload = {
    ...userDetails,
    ...SE,
  };
  try {
    const response = await postAPICall(
      process.env.REACT_APP_LINK + "supplies_receives/create",
      {
        ...payload,
      }
    );
    return { response: response.data };
  } catch (error) {
    return { error: error.response };
  }
};

// UPDATE
export const approveSuppliesExpense = async (id, status) => {
  try {
    var payload = {
      requester: getUser(),
      token: getToken(),
      supplies_expense_id: id,
      new_status: status,
    };
    const response = await postAPICall(
      process.env.REACT_APP_LINK + "supplies_expenses/change_status",
      payload
    );
    
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};

export const searchSE = async (data) => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "supplies_expenses/search",
      {
        requester: getUser(),
        token: getToken(),
        se_date_from: data.date_from,
        se_date_to: data.date_to,
        // branch_id: data.branch_id,
        supplier_id: data.supplier_id,
        vendor_id: data.vendor_id,
        status: data.status,
        order_status: data.order_status,
        limit_by: data.limit_by,
        anything: data.anything,
        type: data.type,
      }
    );
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};

export const changeStatus = async (data) => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "supplies_expenses/change_status",
      {
        requester: getUser(),
        token: getToken(),
        supplies_expense_id: data.se_id,
        new_status: data.new_status,
      }
    );
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};

export const paymentModal = async (
  supplies_expense_id,
  payment,
  balance,
  payee
) => {
  try {
    var payload = {
      requester: getUser(),
      token: getToken(),
      supplies_expense_id: supplies_expense_id,
      amount: payment.paid_amount ? payment.paid_amount : balance,
      payment_type: payment.payment_type,
      payment_date: payment.payment_date,
      deposit_date: payment.deposit_date,
      remarks: payment.remarks,
      from_bank_id: payment.from_bank_id,
      to_bank_id: payment.to_bank_id,
      from_bank_name: payment.bank_name,
      to_bank_name: payment.to_bank_name,
      reference_no: payment.reference_number,
      transaction_number: payment.transaction_number,
      payment_description: payment.payment_description,
      to_account_no: payment.to_account_no,
      to_account_name: payment.to_account_name,
      transaction_fee: payment.transaction_fee,
      payee: payment.payee ? payment.payee : payee,
      particulars: payment.particulars,
      check_no: payment.check_no,
      check_date: payment.check_date,
      issued_date: payment.issued_date,
    };
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "supplies_expenses/add_payment",
      payload
    );
    return { data: response.data };
  } catch (error) {
    return { error: error };
  }
};
