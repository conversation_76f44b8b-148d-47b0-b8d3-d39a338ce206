import { Modal, Container } from "react-bootstrap";
import React, { useEffect, useState } from "react";
import Table from "../../Components/TableTemplate/BreakdownTable";
import { getPaymentByExpense } from "../../Helpers/apiCalls/SalesReport/SalesReportApi";

//css
import "./Modal.css";
import { formatDateNoTime, numberFormat } from "../../Helpers/Utils/Common";

function ExpensesBreakdownModal(props) {
  const [tableData, setTableData] = useState([]); 
  const [loading, setLoading] = useState(false); 

  useEffect(() => {
    if (props.show && props.data.length > 0) {
      fetchPaymentByExpense(props.data[0]); 
    }
  }, [props.show, props.data]);

  async function fetchPaymentByExpense(payload) {
    setLoading(true);
    const response = await getPaymentByExpense(payload);

    if (response.status === 404 || !response.data) {
      // Clear table data if response is 404 or no data is returned
      setTableData([]);
    } else if (response.data) {
      const mappedData = response.data.data.map((item) => ({
        se_id: item.se_id,
        po_date: payload.type === "operating_expenses" ? formatDateNoTime(item.supplies_expense_date) : formatDateNoTime(item.project_expense_date),
        supplier: item.supplier || "N/A",
        amount: numberFormat(item.grand_total),
        payment_date: item.issued_date ? formatDateNoTime(item.issued_date) : "",
        payment_mode: item.payment_mode
          ? item.payment_mode.charAt(0).toUpperCase() + item.payment_mode.slice(1) + (item.bank_name ? ` - ${item.bank_name}` : "")
          : "",
        payment_type: payload.type
      }));

      setTableData(mappedData);
    } else {
      console.error("Failed to fetch expenses breakdown:", response.error);
    }

    setLoading(false);
  }

  function ViewButton(row) {
    if (row.payment_type === "operating_expenses") {
      return (
        <div className="mx-3">
          <a
            href={`/suppliesexpenses/review/${row.se_id}`}
            target="_blank"
            rel="noopener noreferrer"
            className="table-button me-3 p-2 d-inline-block text-decoration-none text-center"
          >
            {`PO-${row.se_id}`}
          </a>
        </div>
      );
    } else {
      return (
        <div className="mx-3">
          <a
            href={`/projectexpense/view/${row.se_id}/paid`}
            target="_blank"
            rel="noopener noreferrer"
            className="table-button me-3 p-2 d-inline-block text-decoration-none text-center"
          >
            {`PE-${row.se_id}`}
          </a>
        </div>
      );
    }
  }


  return (
    <div>
      <Modal
        show={props.show}
        onHide={props.onHide}
        className="modal-cont-receivables"
        size={props.size}
        centered
      >
        <Modal.Header closeButton />
        <Modal.Body>
          <div className="col-sm-12 col-6">
            <div className="col-sm-12">
              <span className="custom-modal-body-title">Expenses Breakdown</span>
            </div>
            <Container fluid className="modal-cont receivableAgingContainer">
              <div className="sales-tbl receivableAging" style={{ overflowX: "auto" }}>
                <Table
                  tableHeaders={[
                    // "PO NO",
                    // "PO DATE",
                    tableData.length > 0 && tableData[0].payment_type === "project_expenses" ? "PE NO" : "PO NO",
                    tableData.length > 0 && tableData[0].payment_type === "project_expenses" ? "PE DATE" : "PO DATE",
                    "SUPPLIER",
                    "AMT",
                    "PYMT DATE",
                    "PYMT MODE"
                  ]}
                  headerSelector={[
                    "se_id",
                    "po_date",
                    "supplier",
                    "amount",
                    "payment_date",
                    "payment_mode",
                  ]}
                  tableData={tableData}
                  ViewButton={(row) => ViewButton(row)}
                  fixedHeader
                  fixedHeaderScrollHeight="40vh"
                  showLoader={loading}
                  withActionData={false}
                  className={{
                    customers: "text-text-start text-break ps-2 align-top",
                  }}
                />
              </div>
            </Container>

            <div className="col-sm-12 mt-3 d-flex justify-content-end">
              <button className="button-tertiary mr-3" onClick={props.onHide}>
                Close
              </button>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </div>
  );
}

ExpensesBreakdownModal.defaultProps = {
  title: "",
  type: "",
  size: "xl",
  withButtons: false,
  withHeader: false,
  show: () => {},
  onHide: () => {},
  onEdit: () => {},
};

export default ExpensesBreakdownModal;