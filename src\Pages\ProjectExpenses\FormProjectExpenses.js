import React, { useState } from "react";
import { Col, Form, Row } from "react-bootstrap";
import { useNavigate, useParams } from "react-router-dom";
import toast from "react-hot-toast";
import {Select as AntSelect} from "antd";
import Select from "react-select";
import "react-autocomplete-input/dist/bundle.css";
import "react-bootstrap-typeahead/css/Typeahead.css";
import ReactLoading from "react-loading";
import upload from "../../Assets/Images/upload.png";

import {
  numberFormat,
  toastStyle,
  numberFormatInt,
} from "../../Helpers/Utils/Common";
import Navbar from "../../Components/Navbar/Navbar";
import "../Purchases/PurchaseOrders/PurchaseOrders.css";
import "./ProjectExpenses.css";
import { Fragment } from "react";
import { useEffect } from "react";

import { getAllProjects } from "../../Helpers/apiCalls/Manage/Projects";
import InputError from "../../Components/InputError/InputError";
import { validateProjectExpense } from "./../../Helpers/Validation/Project/ProjectValidation";
import { getAllExpenseType } from "./../../Helpers/apiCalls/expensetypesApi";
import { getAllSuppliers } from "../../Helpers/apiCalls/suppliersApi";
import dayjs from "dayjs";
import { Upload ,DatePicker} from "antd";
import {
  createProjectExpense,
  updateProjectExpense,
  getProjectExpense,
  getRequesterNames,
} from "./../../Helpers/apiCalls/ProjectInvoice/ProjectExpenseApi";
const { Dragger } = Upload;

function FormProjectExpenses({ add, edit }) {
  let navigate = useNavigate();
  const [inactive, setInactive] = useState(true);
  const [isSubmitClicked, setIsSubmitClicked] = useState(false);
  const { id } = useParams();

  // FRANCHISEE INVOICE DETAILS HANDLER
  const [formValues, setFormValues] = useState({
    amount: 0,
    other_fees: 0,
    project_id_value: {
      label: "",
      value: "",
    },
    project_price: "",
    expense_type_id_value: {
      label: "",
      value: "",
    },
    remarks: "",
    requester_ids: [],
    supplier_id: "",
    requester_name_id: "",
  });

  const [projects, setProjects] = useState([]);
  const [expenseTypes, setExpenseTypes] = useState([]);
  const [allSuppliers, setAllSuppliers] = useState([]);
  const [fileList, setFileList] = useState([]);
  const [file, setFile] = useState([]);
  const [requesterNames, setRequesterNames] = useState([]);

  //ERROR HANDLING
  const [isError, setIsError] = useState({
    project_id: false,
    project_price: false,
    expense_type_id: false,
    amount: false,
    project_expense_date: false,
    requester_name_id: false,
    requester_ids: false,
  });

  async function fetchAllProjects() {
    setProjects([]);
    const response = await getAllProjects();
    if (response.data) {
      let result = response.data.data.data.map((a) => {
        var info = a;
        // a.name =  "project_id";
        a.for = "project_id";
        a.select_value = "project_id_value";
        a.label = a.name;
        a.value = a.id;
        return info;
      });
      setProjects(result);
    }
  }
  
  async function fetchRequesterNames() {
    const response = await getRequesterNames();
    if (response.data) {
      // Transform the data to include label and value properties
      const formattedData = response.data.data.map((requester) => ({
        value: requester.id,
        label: requester.name || requester.full_name || requester.username, // adjust based on your API response
      }));
      setRequesterNames(formattedData);
    }
  }

  async function fetchExpenseTypes() {
    setExpenseTypes([]);

    try {
      const response = await getAllExpenseType();

      // Check if response and response.data exist
      if (response && response.data && Array.isArray(response.data)) {
        var expenses = response.data.sort((a, b) =>
          a.name > b.name ? 1 : b.name > a.name ? -1 : 0
        );

        let result = expenses.map((expense) => {
          var info = expense;
          info.for = "expense_type_id";
          info.select_value = "expense_type_id_value";
          info.label = expense.name;
          info.value = expense.id;
          return info;
        });
        setExpenseTypes(result);
      } else {
        console.error("Invalid expense type response:", response);
        toast.error("Failed to load expense types", {
          style: toastStyle(),
        });
      }
    } catch (error) {
      console.error("Error fetching expense types:", error);
      toast.error("Failed to load expense types", {
        style: toastStyle(),
      });
    }
  }

  async function fetchAllSuppliers() {
    setAllSuppliers([]);
    const response = await getAllSuppliers();
    if (response.data) {
      const res = response.data.data.map((row) => {
        return {
          value: row.id,
          label: row.trade_name,
        };
      });
      setAllSuppliers(res);
    }
  }

  async function fetchProjectExpense() {
    const response = await getProjectExpense(id);
    if (response.data) {
      var data = response.data.data[0];
      data.amount = numberFormatInt(data.amount);
      data.other_fees = numberFormatInt(data.other_fees);
      
      // Transform requester_name_ids to match Select format
      if (data.requester_name_ids) {
        data.requester_ids = data.requester_name_ids.map(person => person.requester_name_id);
      }

      setFormValues(data);
    } else {
      toast.error(response.error.data.messages.error, {
        style: toastStyle(),
      });
    }
  }

  //ADD FUNCTIONS
  async function handleSubmit() {
    if (validateProjectExpense(formValues, setIsError)) {
      setIsSubmitClicked(true);

      // Prepare the data for update
      const updatedData = {
        ...formValues,
        id: id,
        amount: formValues.amount?.toString().replace(/,/g, ""),
        other_fees: formValues.other_fees?.toString().replace(/,/g, ""),
        grand_total: formValues.grand_total?.toString().replace(/,/g, ""),
        // Ensure requester_ids is always an array
        requester_ids: formValues.requester_ids || []
      };

      const response = edit 
        ? await updateProjectExpense(updatedData, fileList)
        : await createProjectExpense(updatedData, fileList);

      if (response.data) {
        toast.success(`Successfully ${edit ? "updated" : "created"} project expense!`, {
          style: toastStyle(),
        });
        setTimeout(() => {
          navigate("/projectexpense");
        }, 1000);
      } else {
        toast.error(`Error ${edit ? "updating" : "creating"} project expense`, {
          style: toastStyle(),
        });
        setIsSubmitClicked(false);
      }
    }
  }

  //HANDLES
  const handleAddDetailsChange = (e, search) => {
    if (search) {
      if (e.for === "project_id") {
        setFormValues((prevState) => ({
          ...prevState,
          [e.for]: e.value,
          [e.select_value]: {
            for: e.for,
            label: e.label,
            value: e.value,
          },
          project_price: e.project_price ?? "0.00",
        }));
      } else {
        setFormValues((prevState) => ({
          ...prevState,
          [e.for]: e.value,
          [e.select_value]: {
            for: e.for,
            label: e.label,
            value: e.value,
          },
        }));
      }
    } else {
      const newList = { ...formValues };
      const { name, value } = e.target;
      newList[name] = value;
      setFormValues(newList);
    }
  };

  const handleBeforeUpload = (file) => {
    setFileList([...fileList, file]);
    return false;
  };

  const handleRemove = (selectedFile) => {
    var newlist = fileList.filter((file) => {
      return selectedFile.uid !== file.uid;
    });
    setFileList(newlist);
  };

  //USE EFFECTS
  useEffect(() => {
    if (edit) {
      fetchProjectExpense();
    }
    fetchAllProjects();
    fetchExpenseTypes();
    fetchAllSuppliers();
    fetchRequesterNames();
  }, []);

  useEffect(() => {
    var grandtotal =
      parseFloat(formValues.amount ? formValues.amount : 0) +
      parseFloat(formValues.other_fees ? formValues.other_fees : 0);
    setFormValues((prevState) => ({
      ...prevState,
      grand_total: grandtotal,
    }));
  }, [formValues.amount, formValues.other_fees]);

  return (
    <div>
      <div className="page">
        <Navbar
          onCollapse={(inactive) => {
            setInactive(inactive);
          }}
          active={"SALES"}
        />
      </div>

      <div className={`manager-container ${inactive ? "inactive" : "active"}`}>
        {/* header */}
        <div className="d-flex justify-content-between align-items-center my-3 pb-4">
          <h1 className="page-title mb-0">
            {add ? "ADD " : "EDIT "}PROJECT EXPENSE
          </h1>
        </div>

        {/* content */}
        <div className="edit-form">
          {/* FRANCHISEE SALES INVOICE DETAILS */}
          <Fragment>
            <Row className="mt-4">
              <Col xs={6}>
                <span className="edit-label">
                  Project Name
                  <label className="badge-required">{` *`}</label>
                </span>
                <Select
                  name="project_id"
                  className="react-select-container"
                  classNamePrefix="react-select"
                  placeholder="Select Project..."
                  options={projects}
                  value={projects.find(
                    (option) => option.value === formValues.project_id
                  )}
                  // defaultValue={projects.find(option => option.value === id)}
                  onChange={(e) => handleAddDetailsChange(e, true)}
                />
                <InputError
                  isValid={isError.project_id}
                  message={"Project is required"}
                />
              </Col>
              <Col xs={3}>
                <span className="edit-label">Project Price</span>
                <Form.Control
                  type="text"
                  disabled
                  name="project_price"
                  className="nc-modal-custom-text"
                  value={
                    formValues.project_price
                      ? numberFormat(formValues.project_price)
                      : ""
                  }
                />
              </Col>
              {/* <Col xs={3}>
                <span className="edit-label">
                  Project Net Sales
                  <label className="badge-required">{` *`}</label>
                </span>
                <Form.Control
                  type="text"
                  disabled
                  name="project_net_sales"
                  className="nc-modal-custom-text"
                  value={
                    formValues.paid_amount
                      ? numberFormat(formValues.paid_amount)
                      : 0.0
                  }
                  onChange={(e) => handleAddDetailsChange(e)}
                />
              </Col> */}
              <Col xs={3}>
                <span className="edit-label">Expense Date</span>
                <label className="badge-required">{` *`}</label>
                <DatePicker
                  className="nc-modal-custom-text-new-datepicker"
                  name="project_expense_date"
                  value={formValues.project_expense_date ? dayjs(formValues.project_expense_date) : null} // Use dayjs for date formatting
                  onChange={(date, dateString) => {
                    setFormValues((prev) => ({
                      ...prev,
                      project_expense_date: dateString, // Update the form state with the selected date
                    }));
                  }}
                  format="YYYY-MM-DD" // Ensure the date format is correctly applied
                />
                <InputError
                  isValid={isError.project_expense_date}
                  message={"Expense date is required"}
                />
              </Col>

            </Row>
            <Row className="mt-4">
              <Col xs={4}>
                <span className="edit-label">Account Type</span>
                <label className="badge-required">{` *`}</label>
                <Select
                  name="type"
                  className="react-select-container"
                  classNamePrefix="react-select"
                  placeholder="Select Account Type..."
                  value={expenseTypes.find(
                    (option) => option.value === formValues.expense_type_id
                  )}
                  options={expenseTypes}
                  // onChange={(e) => handleAddDetailsChange(e, true)}
                  onChange={(e) =>
                    setFormValues((prev) => ({
                      ...prev,
                      expense_type_id: e.value,
                    }))
                  }
                />
                <InputError
                  isValid={isError.expense_type_id}
                  message={"Account Type is required"}
                />
              </Col>
              <Col xs={4}>
                <span className="edit-label">Supplier</span>
              
                <Select
                  name="type"
                  className="react-select-container"
                  classNamePrefix="react-select"
                  options={allSuppliers}
                  onChange={(selectedOption) =>
                    setFormValues((prev) => ({ ...prev, supplier_id: selectedOption?.value }))
                  }
                  value={allSuppliers.find(option => option.value === formValues.supplier_id)}
                  filterOption={(option, inputValue) =>
                    option.label.toLowerCase().includes(inputValue.toLowerCase())
                  }
                  placeholder="Select supplier"
                />
              </Col>
              <Col xs={4}>
                  <span className="edit-label">Requested By</span>
                  <label className="badge-required">{` *`}</label>
                  <Select
                    name="type"
                    className="react-select-container"
                    classNamePrefix="react-select"
                    options={requesterNames}
                    onChange={(selectedOption) =>
                      setFormValues((prev) => ({ 
                        ...prev, 
                        requester_name_id: selectedOption?.value 
                      }))
                    }
                    value={requesterNames.find(option => 
                      option.value === formValues.requester_name_id
                    )}
                    placeholder="Select Requester"
                  />
                  <InputError
                  isValid={isError.requester_name_id}
                  message={"Requester is required"}
                />
                </Col>
            </Row>
            <Row className="mt-4">
              <Col xs={6}>
              <span className="edit-label">
                People Involved
                <span className="edit-optional px-2">(Optional)</span>
              </span>               
                <Select
                  isMulti
                  className="w-100"
                  options={requesterNames}
                  onChange={(selectedOptions) => {
                    setFormValues((prevState) => ({
                      ...prevState,
                      requester_ids: selectedOptions ? selectedOptions.map(option => option.value) : []
                    }));
                  }}
                  value={requesterNames.filter(option => 
                    formValues.requester_ids?.includes(option.value)
                  )}
                  placeholder="Select People"
                />
                {/* <InputError
                  isValid={isError.requester_ids}
                  message={"People Involved is required"}
                /> */}
              </Col>
              <Col xs={6}>
                <span className="edit-label">Remarks</span>
                <Form.Control
                  as="textarea"
                  name="remarks"
                  className="nc-modal-custom-text"
                  value={formValues.remarks}
                  onChange={(e) => handleAddDetailsChange(e)}
                />
              </Col>
            </Row>
          </Fragment>
          <Row className="mt-4 pt-3">
            <Col xs={12}>
              <Dragger
                {...{
                  // style: { width: 550 },
                  file,
                  defaultFileList: file,
                  onRemove: handleRemove,
                  beforeUpload: handleBeforeUpload,
                  multiple: false,
                  // onChange: handleOnChange,
                  listType: "picture",
                  progress: { showInfo: true },
                  data: (file) => {},
                }}
              >
                <img src={upload} className="cursor-pointer" alt="" />
                <p className="ant-upload-text">
                  Click or drag file to this area to upload
                </p>
                {/* <p className="ant-upload-hint">
                            Support for a single or bulk upload.
                        </p>  */}
              </Dragger>
            </Col>
          </Row>
          <Row className="align-right pt-3">
            <Col xs={2} className="text-end">
              <span className="edit-label color-gray">
                Amount
                <label className="badge-required">{` *`}</label>
              </span>
            </Col>
            <Col xs={1} className="text-end">
              <span className="edit-label align-middle">PHP</span>
            </Col>
            <Col xs={3} className="text-end">
              <Form.Control
                type="number"
                name="amount"
                min={0}
                step="0.01"
                value={formValues.amount}
                className="align-middle nc-modal-custom-text text-end"
                onChange={(e) => handleAddDetailsChange(e)}
              />
              <InputError
                isValid={isError.amount}
                message={"amount is required"}
              />
            </Col>
          </Row>
          <Row className="align-right pt-3">
            <Col xs={2} className="text-end">
              <span className="edit-label color-gray">Other Fees</span>
            </Col>
            <Col xs={1} className="text-end">
              <span className="edit-label align-middle">PHP</span>
            </Col>
            <Col xs={3} className="text-end">
              <Form.Control
                type="number"
                name="other_fees"
                min={0}
                step="0.01"
                value={formValues.other_fees}
                className="align-middle nc-modal-custom-text text-end"
                onChange={(e) => handleAddDetailsChange(e)}
              />
              <InputError
                isValid={isError.other_fees}
                message={"Other fee is required"}
              />
            </Col>
          </Row>
          <Row className="align-right py-5">
            <Col xs={2} className="text-end">
              <span className="edit-label color-gray grand-total-text">
                Grand Total
              </span>
            </Col>
            <Col xs={1} className="text-end">
              <span className="edit-label align-middle grand-total-text">
                PHP
              </span>
            </Col>
            <Col xs={3} className="text-end">
              <span className="edit-label align-middle grand-total-text">
                {numberFormat(formValues.grand_total)}
              </span>
            </Col>
          </Row>

          {/* FOOTER: CANCEL & SUBMIT BUTTONS */}
          <div className="d-flex justify-content-end pt-5 pb-3">
            <button
              type="button"
              className="button-secondary me-3"
              onClick={() => navigate("/projectexpense")}
            >
              Cancel
            </button>
            {isSubmitClicked ? (
              <div className="button-primary d-flex justify-content-center">
                <ReactLoading
                  type="bubbles"
                  color="#FFFFFF"
                  height={50}
                  width={50}
                />
              </div>
            ) : formValues.project_id === "" ||
              formValues.project_expense_date === "" ||
              formValues.expense_type_id === "" ? (
              <button type="button" className="button-primary">
                Submit
              </button>
            ) : (
              <button
                type="button"
                className="button-primary"
                onClick={handleSubmit}
              >
                Submit
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

FormProjectExpenses.defaultProps = {
  add: false,
  edit: false,
};

export default FormProjectExpenses;