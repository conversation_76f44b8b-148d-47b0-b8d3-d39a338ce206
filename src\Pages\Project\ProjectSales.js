import React, { useState, useEffect } from "react";
import { Col, Row, Button } from "react-bootstrap";
import { DatePicker } from "antd";
import Navbar from "../../Components/Navbar/Navbar";
import Select from "react-select";
import {
  TokenExpiry,
  numberFormat,
  selectDropdownStyle,
  firstDayOfMonth,
  lastDayOfMonth,
} from "../../Helpers/Utils/Common";
import { CSVLink } from "react-csv";
import Moment from "moment";

// css
import "./Project.css";
import "../Purchases/PurchaseOrders/PurchaseOrders.css";
import {
  searchProject,
  searchProjectSales,
} from "./../../Helpers/apiCalls/Manage/Projects";
import { getAllCustomer } from "./../../Helpers/apiCalls/Manage/CustomerAPI";
import { getDistributor } from "../../Helpers/apiCalls/Distributor/DistributorApi";
import Table from "../../Components/TableTemplate/Table";
import ProjectSaleModal from "../../Components/Modals/ProjectSaleModal";
import { searchProjectInv } from "../../Helpers/apiCalls/projectApi";
import { useNavigate } from "react-router-dom";

const {RangePicker} = DatePicker;

/**
 *  Franchise Register component
 */

export default function ProjectSales() {
  // Get the current date
  const currentDate = Moment();
  const navigate = useNavigate();
  
  const [inactive, setInactive] = useState(true);
  const [tableData, setTableData] = useState([]);
  const [allSales, setAllSales] = useState([]);
  const [projectInvoiceData, setProjectInvoiceData] = useState([]);
  const [projects, setProjects] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [allDistributors, setAllDistributors] = useState([]);
  const [totalSummary, setTotalSummary] = useState({
    total_amount: 0,
    total_receivables: 0,
    total_expenses: 0,
    total_sales: 0,
  })
  const [showLoader, setShowLoader] = useState(false);
  //Expense Modal
  const [showExpenseModal, setShowExpenseModal] = useState(false);
  const handleCloseExpenseModal = () => setShowExpenseModal(false);

  /* FILTER CONFIGS */
  const [filterConfig, setFilterConfig] = useState({
    date_from: null,
    date_to: null,
  });

  async function handleShowExpenseModal(data) {
    fetchProjectInv(data.row.project_id);
    setAllSales(data);
  }

  function handleRowClick(row) {
    navigate(`/projectsalesview/${row.project_id}`);
  }

  async function fetchCustomers() {
    setShowLoader(true);
    setCustomers([]);

    const response = await getAllCustomer();
    if (response.error) {
      if (response.error.data.status !== 404) {
        TokenExpiry(response.error);
      }
    } else {
      var customers = response.data.data.map((data) => {
        var info = {};
        info.label = data.name;
        info.value = data.id;
        return info;
      });
      setCustomers([{ label: "All Customers", value: "" }, ...customers]);
    }
    setShowLoader(false);
  }

  async function fetchProjects() {
    setShowLoader(true);
    setProjects([]);

    const response = await searchProject();
    if (response.error) {
      if (response.error.data.status !== 404) {
        TokenExpiry(response.error);
      }
    } else {
      var projects = response.data.data.map((data) => {
        var info = {};
        info.label = data.name;
        info.value = data.id;
        return info;
      });
      setProjects([{ label: "All Projects", value: "" }, ...projects]);
    }
    setShowLoader(false);
  }

  function handleFilterChange(e) {
    const { name, value } = e.target;
    setFilterConfig((prev) => {
      return { ...prev, [name]: value };
    });
  }

  async function fetchData() {
    setShowLoader(true);
    setTableData([]);
    setTotalSummary({
      total_amount: 0,
      total_receivables: 0,
      total_expenses: 0,
      total_sales: 0,
    })

    const response = await searchProjectSales(filterConfig);
    if (response.data) {
      var tempTotalReceivables = 0;
      var tempTotalExpenses = 0;
      var tempTotalAmount = 0;
      var tempTotalSales = 0;
      var tempTotalPaidAmount = 0;
      
      var allData = response.data.project_sales.map((data) => {
        // Compute totals
        tempTotalAmount += parseFloat(data.amount);
        tempTotalReceivables += parseFloat(data.receivable);
        tempTotalExpenses += parseFloat(data.project_expense);
        tempTotalPaidAmount += parseFloat(data.paid_amount);
      
        // Calculate sales as amount - expenses
        const sales = parseFloat(data.paid_amount) - parseFloat(data.project_expense);
        tempTotalSales += sales;
      
        // Format data for display
        var info = { ...data }; // Ensure info is a copy of data
        info.amount = numberFormat(data.amount);
        info.receivable = numberFormat(data.receivable);
        info.paid_amount = data.paid_amount
          ? numberFormat(data.paid_amount)
          : "0.00";
        info.total_sales = numberFormat(sales) || "0.00";
        info.project_expense = numberFormat(data.project_expense);
      
        return info;
      });
      
      setTableData(allData);
      setTotalSummary({
        total_amount: tempTotalAmount,
        total_receivables: tempTotalReceivables,
        total_expenses: tempTotalExpenses,
        total_sales: tempTotalSales,
      })
    }
    setShowLoader(false);
  }

  async function fetchProjectInv(projectId) {
    const response = await searchProjectInv(projectId);
    if (response.data) {
      setProjectInvoiceData(response.data.data);
      setShowExpenseModal(true);
    }
  }

  async function fetchDistributors() {
    setAllDistributors([]);
    const response = await getDistributor();
    if (response.data) {
      const res = response.data.data.map((row) => {
        return {
          value: row.id,
          label: row.name,
        };
      });
      setAllDistributors([{ value: "", label: "All Distributors" }, ...res]);
    }
  }

  const excelHeaders = [
    { label: "Project Name", key: "name" },
    { label: "Customer", key: "customer_name" },
    { label: "Amount", key: "amount" },
    { label: "Paid Amount", key: "paid_amount" },
    { label: "Receivable", key: "receivable" },
    { label: "Project Expenses", key: "project_expense" },
    { label: "Total Profit", key: "total_sales" },
  ];

  function handleToCSV() {
    return (
      <CSVLink
        data={tableData}
        headers={excelHeaders}
        filename={`ProjectSales_${Moment().format("YYYY-MM-DD")}.csv`}
        style={{ textDecoration: "none", color: "#ffffff" }}
      >
        Export to CSV
      </CSVLink>
    );
  }

  useEffect(() => {
    fetchProjects();
    fetchCustomers();
    fetchDistributors();
  }, []);

  useEffect(() => {
    fetchData();
  }, [filterConfig]);

  return (
    <div>
      <div className="page">
        <Navbar
          onCollapse={(inactive) => {
            setInactive(inactive);
          }}
          active={"FINANCIAL REPORT"}
        />
      </div>

      <div className={`manager-container ${inactive ? "inactive" : "active"}`}>
        {/* headers */}
        <Row className="mb-4">
          <Col xs={6}>
            <h1 className="page-title">PROJECT SALES</h1>
          </Col>
          
          <Col xs={6} className="d-flex justify-content-end">
          <input
              type="search"
              name="anything"
              placeholder="Search..."
              value={filterConfig.anything}
              onChange={(e) => handleFilterChange(e)}
              className="search-bar"
            />
            <button className="add-btn text-center">{handleToCSV()}</button>
          </Col>
        </Row>

        <div className="tab-content">
          <div className="my-2 px-4 PO-filters d-flex custom-dropdown-style">
            <Select
              className="dropsearch-filter px-0 py-0 me-2"
              classNamePrefix="react-select"
              placeholder="Select Payment Structure"
              styles={selectDropdownStyle}
              options={[
                { value: "All", label: "All Payment Structure" },
                { value: "Retail", label: "Retail" },
                { value: "Subscription Based", label: "Subscription Based" },
              ]}
              onChange={(e) => {
                setFilterConfig((prev) => {
                  return { ...prev, payment_structure: e.value };
                });
              }}
            />

            <Select
              className="dropsearch-filter px-0 py-0 me-2"
              classNamePrefix="react-select"
              placeholder="Select Customer"
              styles={selectDropdownStyle}
              options={customers}
              onChange={(e) => {
                setFilterConfig((prev) => {
                  return { ...prev, customer_id: e.value };
                });
              }}
            />
            <Select
              className="dropsearch-filter px-0 py-0 me-2"
              classNamePrefix="react-select"
              placeholder="Select Distributor"
              styles={selectDropdownStyle}
              options={allDistributors}
              onChange={(e) => {
                setFilterConfig((prev) => {
                  return { ...prev, distributor_id: e.value };
                });
              }}
            />
            <RangePicker 
              placeholder={[
                filterConfig.date_from ?? firstDayOfMonth(), 
                filterConfig.date_to ?? lastDayOfMonth()
              ]}
              onChange={(e) => {
                if (e) {
                  setFilterConfig((prev) => ({
                    ...prev,
                    date_from: e[0].format('YYYY-MM-DD'),
                    date_to: e[1].format('YYYY-MM-DD'),
                  }));
                } else {
                  setFilterConfig((prev) => ({
                    ...prev,
                    date_from: null, 
                    date_to: null,
                  }));
                }
              }}
            />
          </div>

          <div className="mt-4 mb-2 px-2 PO-filters d-flex justify-content-center">
            <span className="me-4 ml-4 align-middle ps-label">
              Total Invoice Amount: {numberFormat(totalSummary.total_amount)}
            </span>
            <span className="me-4 ml-4 align-middle ps-label">
              Total Receivables: {numberFormat(totalSummary.total_receivables)}
            </span>
            <span className="me-4 ml-4 align-middle ps-label">
              Total Expenses: {numberFormat(totalSummary.total_expenses)}
            </span>
            <span className="me-4 ml-4 align-middle ps-label">
              Total Profit: {numberFormat(totalSummary.total_sales)}
            </span>
          </div>
          <Table
            tableHeaders={[
              "CUSTOMER",
              "PROJECT NAME",
              "AMOUNT",
              "PAID AMOUNT",
              "RECEIVABLE",
              "PROJECT EXPENSE",
              "TOTAL PROFIT",
            ]}
            headerSelector={[
              "customer_name",
              "name",
              "amount",
              "paid_amount",
              "receivable",
              "project_expense",
              "total_sales",
            ]}
            tableData={tableData}
            showLoader={showLoader}
            withAction={false}
            onRowClicked={handleRowClick}
          />
        </div>
      </div>
      <ProjectSaleModal
        show={showExpenseModal}
        onHide={handleCloseExpenseModal}
        data={projectInvoiceData}
        sales={allSales}
      ></ProjectSaleModal>
    </div>
  );
}
