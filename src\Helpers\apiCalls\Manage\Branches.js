import { getToken, getUser, getTime12 } from "../../Utils/Common";
import { getAPICall, postAPICall } from "../axiosMethodCalls";
import Moment from "moment";

//GET
export const getAllProjects = async () => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "projects/get_all_project",
      {
        requester: getUser(),
        token: getToken(),
      }
    );
    return { data: response };
  } catch (error) {
    return { error: error.response };
  }
};

// GET SINGLE
export const getProject = async (id) => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "projects/get_project",
      {
        requester: getUser(),
        token: getToken(),
        project_id: id,
      }
    );
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};

export const searchProject = async (name) => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "projects/search",
      {
        requester: getUser(),
        token: getToken(),
        name: name,
      }
    );
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};

export const searchProjectStatus = async (filterConfig) => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "project_operation_logs/get_all",
      {
        requester: getUser(),
        token: getToken(),
        status: filterConfig.status,
        branch_type: filterConfig.branch_type,
        project_name: filterConfig.project_name,
        project_id: filterConfig.project_id,
        date: filterConfig.date_from
          ? Moment(filterConfig.date_from).format("YYYY-MM-DD")
          : "",
      }
    );
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};

//POST
export const createBranch = async (data) => {
  try {
    const response = await postAPICall(
      process.env.REACT_APP_LINK + "projects/create",
      {
        requester: getUser(),
        token: getToken(),
        name: data.name,
        company: data.company,
        address: data.address,
        phone_no: data.phone_no,
        tin_no: data.tin_no,
        contact_person: data.contact_person,
      }
    );
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};

export const updateBranch = async (data) => {
  try {
    const response = await postAPICall(
      process.env.REACT_APP_LINK + "projects/update",
      {
        requester: getUser(),
        token: getToken(),
        project_id: data.id,
        name: data.name,
        company: data.company,
        address: data.address,
        phone_no: data.phone_no,
        tin_no: data.tin_no,
        contact_person: data.contact_person,
      }
    );
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};

export const deleteBranch = async (id) => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "projects/delete",
      {
        requester: getUser(),
        token: getToken(),
        project_id: id,
      }
    );
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};
