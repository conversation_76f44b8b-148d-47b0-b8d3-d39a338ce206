import { handleValidationChange } from "../CommonValidation";

export const validateEmployeesEdit = (data, setIsError) => {
    //Required
    // first_name: "",
    // middle_name: "",
    // last_name: "",
    // username: "",
    // password: "",
    //email:"",

    var isValid = true;
    var isValidFirstName = true;
    var isValidLastName = true;
    var isValidUsername = true;
    var isValidPassword = true;
    var isValidConfirmPassword = true;
    var isValidEmail = true;
    var isContactNumber = true;
    var isAddress = true;
    var isGender = true;
    var isBirthdate = true;
    var isCivilStatus = true;
    var isNationality = true;
    var isReligion = true;
    var isEmploymentStatus = true;
    var isSalaryType = true;
    var isSalary = true;

    if (data.first_name === "") {
        handleValidationChange("first_name", true, setIsError);
        isValidFirstName = false;
    } else {
        handleValidationChange("first_name", false, setIsError);
        isValidFirstName = true;
    }

    // if(data.middle_name === "") {
    //     handleValidationChange("middle_name", true, setIsError);
    //     isValid = true;
    // } else {
    //     handleValidationChange("middle_name", false, setIsError);
    // }

    if (data.last_name === "") {
        handleValidationChange("last_name", true, setIsError);
        isValidLastName = false;
    } else {
        handleValidationChange("last_name", false, setIsError);
        isValidLastName = true;
    }

    // if(data.type === "3" && branches.length === 0) {
    //     handleValidationChange("branches", true, setIsError);
    //     isValid = false;
    // } else {
    //     handleValidationChange("branches", false, setIsError);
    // }

    // if(data.user_type === "") {
    //     handleValidationChange("user_type", true, setIsError);
    //     isValid = false;
    // } else {
    //     handleValidationChange("user_type", false, setIsError);
    // }

    if (data.username === "") {
        handleValidationChange("username", true, setIsError);
        isValidUsername = false;
    } else {
        handleValidationChange("username", false, setIsError);
        isValidUsername = true;
    }
    if (data.email === "") {
        handleValidationChange("email", true, setIsError);
        isValidEmail = false;
    } else {
        handleValidationChange("email", false, setIsError);
        isValidEmail = true;
    }

    // if (data.password === "") {
    //     handleValidationChange("password", true, setIsError);
    //     isValidPassword = false;
    // } else {
    //     handleValidationChange("password", false, setIsError);
    //     isValidPassword = true;
    // }

    // if (data.confirm_password === "") {
    //     handleValidationChange("confirm_password", true, setIsError);
    //     isValidConfirmPassword = false;
    // } else {
    //     handleValidationChange("confirm_password", false, setIsError);
    //     isValidConfirmPassword = true;
    // }

    if (data.contact_no === "" || data.contact_no === null || data.contact_no === undefined) {
        handleValidationChange("contact_no", true, setIsError);
        isContactNumber = false;
    } else {
        handleValidationChange("contact_no", false, setIsError);
        isContactNumber = true;
    }

    if (data.address === "" || data.address === null || data.address === undefined) {
        handleValidationChange("address", true, setIsError);
        isAddress = false;
    } else {
        handleValidationChange("address", false, setIsError);
        isAddress = true;
    }

    if (data.gender === "" || data.gender === null || data.gender === undefined) {
        handleValidationChange("gender", true, setIsError);
        isGender = false;
    } else {
        handleValidationChange("gender", false, setIsError);
        isGender = true;
    }

    if (data.birthdate === "" || data.birthdate === null || data.birthdate === undefined) {
        handleValidationChange("birthdate", true, setIsError);
        isBirthdate = false;
    } else {
        handleValidationChange("birthdate", false, setIsError);
        isBirthdate = true;
    }

    if (data.civil_status === "" || data.civil_status === null || data.civil_status === undefined) {
        handleValidationChange("civil_status", true, setIsError);
        isCivilStatus = false;
    } else {
        handleValidationChange("civil_status", false, setIsError);
        isCivilStatus = true;
    }

    if (data.nationality === "" || data.nationality === null || data.nationality === undefined) {
        handleValidationChange("nationality", true, setIsError);
        isNationality = false;
    } else {
        handleValidationChange("nationality", false, setIsError);
        isNationality = true;
    }

    if (data.religion === "" || data.religion === null || data.religion === undefined) {
        handleValidationChange("religion", true, setIsError);
        isReligion = false;
    } else {
        handleValidationChange("religion", false, setIsError);
        isReligion = true;
    }

    if (data.employment_status === "" || data.employment_status === null || data.employment_status === undefined) {
        handleValidationChange("employment_status", true, setIsError);
        isEmploymentStatus = false;
    } else {
        handleValidationChange("employment_status", false, setIsError);
        isEmploymentStatus = true;
    }

    if (data.salary_type === "" || data.salary_type === null || data.salary_type === undefined) {
        handleValidationChange("salary_type", true, setIsError);
        isSalaryType = false;
    } else {
        handleValidationChange("salary_type", false, setIsError);
        isSalaryType = true;
    }

    if (data.salary === "" || data.salary === null || data.salary === undefined) {
        handleValidationChange("salary", true, setIsError);
        isSalary = false;
    } else {
        handleValidationChange("salary", false, setIsError);
        isSalary = true;
    }

    isValid =
        isValidFirstName &&
        isValidLastName &&
        isValidUsername &&
        isContactNumber &&
        isAddress &&
        isGender &&
        isBirthdate &&
        isCivilStatus &&
        isNationality &&
        isReligion &&
        isEmploymentStatus &&
        isSalaryType &&
        isSalary &&
        isValidEmail;

    return isValid;
};
