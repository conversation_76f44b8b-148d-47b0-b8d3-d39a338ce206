import React, { useState } from "react";
import { Col, Container, Modal, Row } from "react-bootstrap";
import { useNavigate, useParams } from "react-router-dom";
import toast from "react-hot-toast";
import "react-autocomplete-input/dist/bundle.css";
import "react-bootstrap-typeahead/css/Typeahead.css";
import "react-responsive-carousel/lib/styles/carousel.min.css";
import NoDataImg from "../../Assets/Images/no-data-dog.png";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

import {
  capitalizeFirstLetter,
  formatDateNoTime,
  numberFormat,
  refreshPage,
  toastStyle,
} from "../../Helpers/Utils/Common";
import Navbar from "../../Components/Navbar/Navbar";
import "../Purchases/PurchaseOrders/PurchaseOrders.css";
import "./ProjectExpenses.css";
import { Fragment } from "react";
import { useEffect } from "react";
import {
  getProjectExpense,
  updateStatusProjectExpense,
} from "./../../Helpers/apiCalls/ProjectInvoice/ProjectExpenseApi";
import moment from "moment";
import PaymentTable from "../ProjectInvoice/PaymentTable";



function ViewProjectExpenses() {
  let navigate = useNavigate();
  const [inactive, setInactive] = useState(true);

  const { id, status } = useParams();

  // FRANCHISEE INVOICE DETAILS HANDLER
  const [addDetails, setAddDetails] = useState({});
  const [data, setData] = useState([]);
  const [paymentInfo, setPaymentInfo] = useState([]);

  const [showPaymentAttachmentModal, setShowPaymentAttachmentModal] = useState(false);
  const [selectedPaymentAttachment,setSelectedPaymentAttachment] = useState(null);

  async function fetchProjectExpense() {
    const response = await getProjectExpense(id);
    if (response.data) {
      setData(response.data.data || []);
      var data = response.data.data[0];
      setAddDetails(data);


      var payment = data.project_expense_payments?.map((data) => {
        if (data.amount !== "0.00") {
          var info = data;
          info.payment_date = formatDateNoTime(data.payment_date);
          info.amount = numberFormat(data.amount);
          info.deposit_date = data?.deposit_date ? formatDateNoTime(data.deposit_date) : "";
          info.deposit_to = data?.deposit_to ? data.deposit_to : "";
          info.payment_method = capitalizeFirstLetter(data.payment_method);
          // if (data.payment_method === "check") {
          //   info.payment_method = data.payment_method + ((data.cheque_number ?? "") ? " - " + data.cheque_number : "");
          // } else if (data.payment_method === 'bank'){
          //   info.payment_method = data.payment_method + ((data.reference_number ?? "") ? " - " + data.reference_number : "");
          // } else {
          //   info.payment_method = data.payment_method;
          // }
          return info;
        }
        return null;
      }).filter(Boolean);

      setPaymentInfo(payment || []);
    } else {
      toast.error(response.error.data.messages.error, {
        style: toastStyle(),
      });
    }
  }

  function Print() {
    let printContents = document.getElementById("printablediv").innerHTML;
    let originalContents = document.body.innerHTML;
    document.body.innerHTML = printContents;
    window.print(printContents);
    document.body.innerHTML = originalContents;
    // refreshPage();
  }

  async function handlePrint() {
    toast.loading("Printing project invoice...", { style: toastStyle() });
    setTimeout(() => {
      toast.dismiss();
      Print();
    }, 1000);
  }

  async function updateStatus(id, status) {
    const response = await updateStatusProjectExpense(id, status);
    if (response.data) {
        toast.success("Successfully Updated Project Invoice Status")
        setTimeout(() => navigate("/projectexpense"), 1000);
    } else {
      toast.error("Error Updating Project Invoice", {
        style: toastStyle(),
      });
    }
  }

  const handlePaymentAttachmentPreview = (payment) => {
    if (payment && payment.attachments && payment.attachments[0]) {
      const attachmentData = {
        name: payment.attachments[0].file_name || "Attachment",
        file_url: payment.attachments[0].file_url,
      };
      setSelectedPaymentAttachment(attachmentData);
      setShowPaymentAttachmentModal(true);
    }
  };

  useEffect(() => {
    fetchProjectExpense();
  }, []);

  return (
    <div>
      <div className="page">
        <Navbar
          onCollapse={(inactive) => {
            setInactive(inactive);
          }}
          active={"SALES"}
        />
      </div>

      <div className={`manager-container ${inactive ? "inactive" : "active"}`}>
        {/* header */}
        <div className="d-flex justify-content-between align-items-center my-3 pb-4">
          <h1 className="page-title mb-0">VIEW PROJECT EXPENSE</h1>
        </div>

        {/* content */}
        <div className="edit-form">
          {/* FRANCHISEE SALES INVOICE DETAILS */}
          <div id="printablediv">
            <Fragment>
              <Row className="review-container py-3">
                <Row>
                  <Col xs={4}>
                    <span className="review-label">Project Name</span>
                  </Col>
                  <Col xs={3}>
                    <span className="review-label">Project Price</span>
                  </Col>
                  <Col xs={3}>
                    <span className="review-label">Expense Type</span>
                  </Col>
                  <Col xs={2}>
                    <span className="review-label">Expense Date</span>
                  </Col>
                </Row>
                <Row>
                  <Col xs={4}>
                    <span className="review-data">
                      {addDetails.project_name}
                    </span>
                  </Col>
                  <Col xs={3}>
                    <span className="review-data">
                      {addDetails.project_price
                        ? numberFormat(addDetails.project_price)
                        : ""}
                    </span>
                  </Col>
                  <Col xs={3}>
                    <span className="review-data">
                      {addDetails.expense_type_name}
                    </span>
                  </Col>
                  <Col xs={2}>
                    <span className="review-data">
                      {addDetails.project_expense_date?moment(addDetails.project_expense_date).format('MMM DD, YYYY'):""}
                    </span>
                  </Col>
                </Row>
              </Row>
              <Row className="review-container py-3">
                <Row>
                  <Col xs={4}>
                    <span className="review-label">Supplier</span>
                  </Col>
                  <Col xs={3}>
                    <span className="review-label">Requested By</span>
                  </Col>
                  <Col xs={3}>
                    <span className="review-label">People Involved</span>
                  </Col>
                  <Col xs={2}>
                    <span className="review-label">Remarks</span>
                  </Col>
                </Row>
                <Row>
                  <Col xs={4}>
                    <span className="review-data">{addDetails.supplier_name}</span>
                  </Col>
                  <Col xs={3}>
                    <span className="review-data">{addDetails.requester_name}</span>
                  </Col>
                  <Col xs={3}>
                    <span className="review-data">
                      {addDetails.requester_name_ids && addDetails.requester_name_ids.length > 0 
                        ? addDetails.requester_name_ids.map(person => person.requester_name).join(', ')
                        : 'No People Involved'}
                    </span>
                  </Col>
                  <Col xs={2}>
                    <span className="review-data">{addDetails.remarks}</span>
                  </Col>
                </Row>
              </Row>
            </Fragment>
            <Row className="mt-4 pt-3">
              <Col xs={12}></Col>
            </Row>
            <Row className="align-right pt-3">
              <Col xs={2} className="text-end">
                <span className="edit-label color-gray">Amount</span>
              </Col>
              <Col xs={1} className="text-end">
                <span className="edit-label align-middle">PHP</span>
              </Col>
              <Col xs={3} className="text-end">
                <span className="edit-label color-gray">
                  {numberFormat(addDetails.amount)}
                </span>
              </Col>
            </Row>
            <Row className="align-right pt-3">
              <Col xs={2} className="text-end">
                <span className="edit-label color-gray">Other fees</span>
              </Col>
              <Col xs={1} className="text-end">
                <span className="edit-label align-middle">PHP</span>
              </Col>
              <Col xs={3} className="text-end">
                <span className="edit-label color-gray">
                  {numberFormat(addDetails.other_fees)}
                </span>
              </Col>
            </Row>
            <Row className="align-right py-5">
              <Col xs={2} className="text-end">
                <span className="edit-label color-gray grand-total-text">
                  Grand Total
                </span>
              </Col>
              <Col xs={1} className="text-end">
                <span className="edit-label align-middle grand-total-text">
                  PHP
                </span>
              </Col>
              <Col xs={3} className="text-end">
                <span className="edit-label align-middle grand-total-text">
                  {numberFormat(addDetails.grand_total)}
                </span>
              </Col>
            </Row>
          </div>
          <Row className="py-3">
            <span className="edit-label color-gray">ATTACHMENT/S</span>
            <Col className="print-table">
              {data?.length > 0 ? (
                data.map((data) => {
                    return (
                        <Row className="px-5 py-2"><FontAwesomeIcon icon="fa-regular fa-file" /><a href={data.base_64} download={data.name} className="edit-label">{data.name}</a></Row>
                    )
                })
              ) : (
                <div className="no-data-cont">
                  <div
                    className="mt-5 mb-2"
                    style={{ textAlignLast: "center" }}
                    alt="no data found"
                  >
                    <img src={NoDataImg} width={100} height={100} />
                  </div>
                  <span>Uh Oh! No data found.</span>
                </div>
              )}
            </Col>
          </Row>
        </div>
        {status === "paid" && (
        <>
          <div>
            <Container
              fluid
              className="PI-payment-info-wrapper mt-5 py-3 px-3 edit-form"
            >
              <h5 className="PI-payment-info">PAYMENT HISTORY</h5>
              <div className="sales-tbl justify-content-center">
                <PaymentTable
                  tableHeaders={[
                    "PYMT ID",
                    "PYMT DATE",
                    "TYPE",
                    "PAID AMT",
                    "ATTACHMENTS"
                  ]}
                  headerSelector={[
                    "id",
                    "payment_date",
                    "payment_method",
                    "amount",
                    "attachments"
                  ]}
                  tableData={paymentInfo}
                  onAttachmentClick={handlePaymentAttachmentPreview}
                />
              </div>
            </Container>
          </div>
        </>
        )}
        {/* FOOTER: CANCEL & SUBMIT BUTTONS */}
        {status === "pending" ? (
          <div className="d-flex justify-content-end pt-4 pb-4">
            <button
              type="button"
              className="button-secondary green-btn me-3"
              onClick={() => updateStatus(id, "approved")}
            >
              Approve
            </button>
            <button
              type="button"
              className="button-secondary red-btn me-3"
              onClick={() => updateStatus(id, "disapproved")}
            >
              Disapprove
            </button>
            <button
              type="button"
              className="button-secondary me-3"
              onClick={handlePrint}
            >
              Print
            </button>
            <button
              type="button"
              className="button-secondary me-3"
              onClick={() => navigate("/projectexpense")}
            >
              Close
            </button>
          </div>
        ) : (
          <div className="d-flex justify-content-end pt-4 pb-4">
            <button
              type="button"
              className="button-secondary me-3"
              onClick={() => navigate("/projectexpense")}
            >
              Close
            </button>
          </div>
        )}

        <Modal
          show={showPaymentAttachmentModal}
          onHide={() => setShowPaymentAttachmentModal(false)}
          centered
          size="lg"
        >
          <Modal.Header closeButton>
            <Modal.Title>
              {selectedPaymentAttachment?.name || "Attachment"}
            </Modal.Title>
          </Modal.Header>
          <Modal.Body className="d-flex justify-content-center">
            {selectedPaymentAttachment && (
              <img
                src={selectedPaymentAttachment.file_url}
                alt="Attachment"
                style={{ 
                  maxWidth: '450px', 
                  height: 'auto',
                  objectFit: 'contain' 
                }}
              />
            )}
          </Modal.Body>
        </Modal>
      </div>
    </div>
  );
}

ViewProjectExpenses.defaultProps = {
  add: false,
  edit: false,
};

export default ViewProjectExpenses;
