import React, { useState, useEffect } from "react";
import { But<PERSON>, Col, Row, Table, Modal, Container } from "react-bootstrap";
import { useNavigate, useParams } from "react-router-dom";
import Navbar from "../../../../Components/Navbar/Navbar";
import "../../PurchaseOrders/PurchaseOrders.css";
import {
  capitalizeFirstLetter,
  dateFormat,
  formatDateNoTime,
  formatDateSlash,
  numberFormat,
  refreshPage,
  toastStyle,
  TokenExpiry,
} from "../../../../Helpers/Utils/Common";
import toast from "react-hot-toast";
import {
  approveSuppliesExpense,
  getSingleSuppliesExpense,
} from "../../../../Helpers/apiCalls/Purchases/suppliesExpensesApi";
import { getType } from "../../../../Helpers/Utils/Common";
import SEModal from "./SEModal";
import PaymentTable from "../../../ProjectInvoice/PaymentTable";

export default function ReviewSuppliesExpense() {
  const { id } = useParams();
  let navigate = useNavigate();

  /* Disapprove Modal */
  const [showDisapproveModal, setShowDisapproveModal] = useState(false);
  const handleShowDisapproveModal = () => setShowDisapproveModal(true);
  const handleCloseDisapproveModal = () => setShowDisapproveModal(false);

  /* Approve Modal */
  const [showApproveModal, setShowApproveModal] = useState(false);
  const handleShowApproveModal = () => setShowApproveModal(true);
  const handleCloseApproveModal = () => setShowApproveModal(false);

  /* Pending Modal */
  const [showPendingModal, setShowPendingModal] = useState(false);
  const handleShowPendingModal = () => setShowPendingModal(true);
  const handleClosePendingModal = () => setShowPendingModal(false);

  const [inactive, setInactive] = useState(true);
  const [reviewSE, setReviewSE] = useState([]);
  const [items, setItems] = useState([]);
  const [status, setStatus] = useState("");
  const [paymentInfo, setPaymentInfo] = useState([]);
  const [attachedFile, setAttachedFile] = useState(null);

  const [showAttachmentModal, setShowAttachmentModal] = useState(false);
  const [selectedAttachment, setSelectedAttachment] = useState(null);
  const [showPaymentAttachmentModal, setShowPaymentAttachmentModal] = useState(false);
  const [selectedPaymentAttachment,setSelectedPaymentAttachment] = useState(null);


  const handleAttachmentPreview = (attachment) => {
    if (attachment && attachment.base64) {
      const attachmentData = {
        name: attachment.name || "Attachment",
        base_64: attachment.base64.includes('data:') 
          ? attachment.base64 
          : `data:image/jpeg;base64,${attachment.base64}`
      };
      setSelectedAttachment(attachmentData);
      setShowAttachmentModal(true);
    }
  };

  /** GET API - Get single supplies expense **/
  async function fetchSingleSuppliesExpense() {
    const response = await getSingleSuppliesExpense(id);
    if (response.data) {
      var data = response.data.data[0];
      // Handle attachments
      if (data.attachments && data.attachments.length > 0) {
        setAttachedFile({
          name: data.attachments[0].name,
          base64: data.attachments[0].base_64
        });
      }
      // Add default due_date handling
      data.due_date = data.due_date || data.supplies_expense_date; // Fallback to supplies_expense_date if due_date is null
      
      data.invoice_no = data.invoice_no?.map((invoice) => {
        return invoice.invoice_no ? invoice.invoice_no : "N/A";
      });
      data.invoice_id = data.invoice_no?.map((invoice) => {
        return invoice.id ? invoice.id : "";
      });

      var payment = data.supplies_expense_payments?.map((data) => {
        if (data.amount !== "0.00") {
          var info = data;
          info.payment_date = formatDateNoTime(data.payment_date);
          info.amount = numberFormat(data.amount);
          info.deposit_date = data?.deposit_date ? formatDateNoTime(data.deposit_date) : "";
          info.deposit_to = data?.deposit_to ? data.deposit_to : "";
          info.payment_method = capitalizeFirstLetter(data.payment_method);
          // if (data.payment_method === "check") {
          //   info.payment_method = data.payment_method + ((data.cheque_number ?? "") ? " - " + data.cheque_number : "");
          // } else if (data.payment_method === 'bank'){
          //   info.payment_method = data.payment_method + ((data.reference_number ?? "") ? " - " + data.reference_number : "");
          // } else {
          //   info.payment_method = data.payment_method;
          // }
          return info;
        }
        return null;
      }).filter(Boolean);

      setPaymentInfo(payment || []);

      var items = data.se_items.map((item) => {
        var info = item;
        info.qty = parseInt(item.qty);
        info.received_qty = item.received_qty ? parseInt(item.received_qty) : 0;
        info.remaining_qty = item.remaining_qty
          ? parseInt(item.remaining_qty)
          : 0;
        info.current_qty = item.current_qty ? parseInt(item.current_qty) : 0;
        info.unit = item.unit;
        info.price = `PHP ${numberFormat(item.price)}`;
        info.amount = `PHP ${numberFormat(item.total)}`;
        info.remarks = item.remarks || "-";
        info.last_received_date = item.last_received_date
          ? formatDateSlash(item.last_received_date)
          : "--/--/---";
        info.last_received_by = item.last_received_by
          ? item.last_received_by
          : "N/A";
        return info;
      });
      setReviewSE(data);
      setItems(items);
    } else if (response.error) {
      TokenExpiry(response);
    }
  }

  async function handlePrint() {
    toast.loading("Printing Pending Supplies Purchase Order...", { style: toastStyle() });
    setTimeout(() => {
      toast.dismiss();
      Print();
    }, 1000);
  }

  function approveSE(state) {
    if (state === "disapproved") {
      handleShowDisapproveModal();
    } else if (state === "approved") {
      handleShowApproveModal();
    } else {
      handleShowPendingModal();
    }

    setStatus(state);
  }

  function Print() {
    let printContents = document.getElementById("printablediv").innerHTML;
    let originalContents = document.body.innerHTML;
    document.body.innerHTML = printContents;
    window.print(printContents);
    document.body.innerHTML = originalContents;
    refreshPage();
  }

  useEffect(() => {
    fetchSingleSuppliesExpense();
  }, []);

  const headersIncompleteOrComplete = [
    "Item",
    " Quantity",
    // "Current Qty",
    // "Unit",
    "Unit Price",
    "Amount",
    "Remarks",
    // "Latest Invoice Date",
    // "Received by",
  ];

  const selectorsIncompleteOrComplete = [
    "name",
    "qty",
    // "current_qty",
    // "unit",
    "price",
    "amount",
    "remarks",
    // "last_received_date",
    // "last_received_by",
  ];

  const headers = [
    "Item",
    "Quantity",
    "Unit Price",
    "Amount",
    "Remarks",
  ];

  const selectors = [
    "name",
    "qty",
    "price",
    "amount",
    "remarks",
  ];

  function renderTable() {
    const isCompleteOrIncomplete =
      reviewSE.order_status === "incomplete" || reviewSE.order_status === "complete";

    const headersToUse = isCompleteOrIncomplete ? headersIncompleteOrComplete : headers;
    const selectorsToUse = isCompleteOrIncomplete ? selectorsIncompleteOrComplete : selectors;

    return (
      <>
        <Table>
          <colgroup>
            <col style={{ width: "35%" }} />
            <col style={{ width: "5%" }} />
            <col style={{ width: "20%" }} />
            <col style={{ width: "20%" }} />
            <col style={{ width: "20%" }} />
          </colgroup>
          <thead>
            <tr>
              {headersToUse.map((header, index) => (
                <th
                  key={index}
                  className={
                    header === "Unit Price" || header === "Amount" || header === "Quantity"
                      ? "text-end"
                      : ""
                  }
                >
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {items.map((item) => (
              <tr key={item.id}>
                {selectorsToUse.map((selector, index) => (
                  <td
                    key={index}
                    className={
                      selector === "price" || selector === "amount" || selector === "qty"
                        ? "text-end"
                        : ""
                    }
                  >
                    {item[selector]}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </Table>
      </>
    );
  }

  const handlePaymentAttachmentPreview = (payment) => {
    if (payment && payment.attachments && payment.attachments[0]) {
      const attachmentData = {
        name: payment.attachments[0].file_name || "Attachment",
        file_url: payment.attachments[0].file_url,
      };
      setSelectedPaymentAttachment(attachmentData);
      setShowPaymentAttachmentModal(true);
    }
  };

  async function submitApproveSE() {
    const response = await approveSuppliesExpense(id, status);
    var text = status === "pending" ? "Returned to Pending" : status;

    if (response.data.response) {
      toast.success("Supplies Expense " + text + " Successfully", {
        style: toastStyle(),
      });
      
      // Updated navigation logic
      if (status === "approved") {
        setTimeout(() => navigate("/suppliesexpenses?tab=incomplete"), 1000);
      } else if (status === "disapproved") {
        setTimeout(() => navigate("/suppliesexpenses"), 1000);
      } else {
        setTimeout(() => navigate("/suppliesexpenses"), 1000);
      }
    } else if (response.error) {
      toast.error("Error Changing Status for Supplies Expense No. " + id, {
        style: toastStyle(),
      });
      setTimeout(() => refreshPage(), 1000);
    }
  }

  function renderButtons() {
    return (
      <>
        <button
          type="button"
          className="button-warning me-3"
          onClick={() => navigate("/suppliesexpenses")}
        >
          <span>Close</span>
        </button>
        <button
          className="button-secondary me-3"
          onClick={handlePrint}
        >
          Print
        </button>
        {reviewSE.status === "for_approval" && (
          <>
            <button
              type="button"
              className="button-primary me-3"
              onClick={() => {
                setStatus("approved");
                handleShowApproveModal();
              }}
            >
              Approve
            </button>
            <button
              type="button" 
              className="button-warning"
              onClick={() => {
                setStatus("disapproved");
                handleShowDisapproveModal();
              }}
            >
              Disapprove
            </button>
          </>
        )}
      </>
    );
  }

  return (
    <div>
      <div className="page">
        <Navbar
          onCollapse={(inactive) => {
            setInactive(inactive);
          }}
          active={"EXPENSE"}
        />
      </div>
      <div className={`manager-container ${inactive ? "inactive" : "active"}`}>
        <div id="printablediv">
          <div className="d-flex justify-content-between">
            <h1 className="page-title mb-4">REVIEW PURCHASE ORDER</h1>
            <div className="review-po mt-5">
              <span className="pe-5">PURCHASE ORDER NO.</span>
              <span>{id}</span>
            </div>
          </div>

          <div className="review-form mb-3">
            <Row className="review-container py-3">
              <Row>
                <Col xs={3}>
                  <span className="review-label">Supplier Name</span>
                </Col>
                <Col xs={3}>
                  <span className="review-label">Type</span>
                </Col>
                <Col xs={3}>
                  <span className="review-label">Purchase Date</span>
                </Col>
                <Col xs={3}>
                  <span className="review-label">Due Date</span>
                </Col>
              </Row>
              <Row>
                <Col xs={3}>
                  <span className="review-data">
                    {reviewSE.supplier_trade_name ||
                      reviewSE.vendor_trade_name ||
                      "N/A"}
                  </span>
                </Col>
                <Col xs={3}>
                  <span className="review-data">{reviewSE.expense_name}</span>
                </Col>
                <Col xs={3}>
                  <span className="review-data">
                    {dateFormat(reviewSE.supplies_expense_date)}
                  </span>
                </Col>
                <Col xs={3}>
                  <span className="review-data">
                    {dateFormat(reviewSE.due_date)}
                  </span>
                </Col>
              </Row>
            </Row>
            <Row className="review-container py-3">
              <Row>
              <Col xs={3}>
                  <span className="review-label">Requisitioner</span>
                </Col>
                <Col xs={3}>
                  <span className="review-label">Forwarder</span>
                </Col>
                {/* <Col xs={3}>
                  <span className="review-label">Payment Method</span>
                </Col> */}
                <Col xs={3}>
                  <span className="review-label">Remarks</span>
                </Col>
              </Row>
              <Row>
                <Col xs={3}>
                  <span className="review-data">{reviewSE.requisitioner_name}</span>
                </Col>
                <Col xs={3}>
                  <span className="review-data">{reviewSE.forwarder_name}</span>
                </Col>
                {/* <Col xs={3}>
                  <span className="review-data">{reviewSE.payment_method}</span>
                </Col> */}
                <Col xs={3}>
                  <span className="review-data">{reviewSE.remarks}</span>
                </Col>
              </Row>
            </Row>
            <Row className="review-container py-3">
              <Row>
                <Col>
                  <span className="review-label">ATTACHED FILE</span>
                </Col>
              </Row>
              <Row className="mt-2">
                <Col>
                  {attachedFile ? (
                    <div className="d-flex align-items-center">
                      <span 
                        className="print-data text-primary"
                        onClick={() => handleAttachmentPreview(attachedFile)}
                        style={{ 
                          cursor: 'pointer',
                          textDecoration: 'underline'
                        }}
                      >
                        {attachedFile.name || 'View Attachment'}
                      </span>
                    </div>
                  ) : (
                    <span className="print-data">No file attached</span>
                  )}
                </Col>
              </Row>
            </Row>
            <div className="mt-4 d-flex flex-column">
              <span className="review-data mb-2">PURCHASED ITEMS</span>
              <div className="review-purchased-items">{renderTable()}</div>
              <div className="d-flex justify-content-end mt-4">
                <div className="print-table-footer-label grand-label" style={{marginRight: "100px"}}>
                  GRAND TOTAL: 
                  <span className="mx-2 print-table-footer-data grand-label">
                    PHP {numberFormat(reviewSE.grand_total)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {reviewSE?.order_status === "complete" && (
        <>
          <div>
            <Container
              fluid
              className="PI-payment-info-wrapper mt-5 py-3 px-3 edit-form"
            >
              <h5 className="PI-payment-info">PAYMENT HISTORY</h5>
              <div className="sales-tbl justify-content-center">
                <PaymentTable
                  tableHeaders={[
                    "PYMT ID",
                    "PYMT DATE",
                    "TYPE",
                    "PAID AMT",
                    "ATTACHMENTS"
                  ]}
                  headerSelector={[
                    "id",
                    "payment_date",
                    "payment_method",
                    "amount",
                    "attachments"
                  ]}
                  tableData={paymentInfo}
                  onAttachmentClick={handlePaymentAttachmentPreview}
                />
              </div>
            </Container>
          </div>
        </>
        )}

        <div className="d-flex justify-content-end mt-4">
          {renderButtons()}
        </div>

        <SEModal
          show={showApproveModal}
          hide={handleCloseApproveModal}
          type="approve"
          handler={submitApproveSE}
        />
        <SEModal
          show={showDisapproveModal}
          hide={handleCloseDisapproveModal}
          type="disapprove"
          handler={submitApproveSE}
        />
        <SEModal
          show={showPendingModal}
          hide={handleClosePendingModal}
          type="return"
          handler={submitApproveSE}
        />

        <Modal
          show={showAttachmentModal}
          onHide={() => setShowAttachmentModal(false)}
          centered
          size="lg"
        >
          <Modal.Header closeButton>
            <Modal.Title>
              {selectedAttachment?.name || "Attachment"}
            </Modal.Title>
          </Modal.Header>
          <Modal.Body className="d-flex justify-content-center">
            {selectedAttachment && (
              <img
                src={selectedAttachment.base_64}
                alt="Attachment"
                style={{ 
                  maxWidth: '450px', 
                  height: 'auto',
                  objectFit: 'contain' 
                }}
              />
            )}
          </Modal.Body>
        </Modal>

        <Modal
          show={showPaymentAttachmentModal}
          onHide={() => setShowPaymentAttachmentModal(false)}
          centered
          size="lg"
        >
          <Modal.Header closeButton>
            <Modal.Title>
              {selectedPaymentAttachment?.name || "Attachment"}
            </Modal.Title>
          </Modal.Header>
          <Modal.Body className="d-flex justify-content-center">
            {selectedPaymentAttachment && (
              <img
                src={selectedPaymentAttachment.file_url}
                alt="Attachment"
                style={{ 
                  maxWidth: '450px', 
                  height: 'auto',
                  objectFit: 'contain' 
                }}
              />
            )}
          </Modal.Body>
        </Modal>
      </div>
    </div>
  );
}
