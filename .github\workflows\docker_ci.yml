name: Dockerhub Publish

on:
  push:
    branches: [ master, main ]
    #   - master  # Change this to your main branch name

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v2

    - name: Login to Docker Hub
      run: docker login -u ${{ secrets.DOCKERHUB_USERNAME }} -p ${{ secrets.DOCKERHUB_PASSWORD }}

    - name: Build and push Docker image
      run: |
        docker build -t mytdev/myt_erp:latest -f Dockerfile .
        docker push mytdev/myt_erp:latest
