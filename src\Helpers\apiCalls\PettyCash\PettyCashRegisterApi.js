import { getToken, getUser } from "../../Utils/Common";
import { getAPICall, postAPICall } from "../axiosMethodCalls";
import Moment from "moment";

const user = getUser();
const token = getToken();

// TABLE / MANAGE

export const getPettyCashInfo = async (params) => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "petty_cash_reports/get_petty_cash",
      {
        token: token,
        requester: user,
        petty_cash_id: params.petty_cash_id,
      }
    );
    return response.data;
  } catch (error) {
    return { error: error.response };
  }
};

export const getNoOfPettyCashRequest = async () => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK +
        "petty_cash_reports/get_petty_cash_status_frequency",
      {
        token: token,
        requester: user,
      }
    );
    return response.data;
  } catch (error) {
    return { error: error.response };
  }
};

export const getPettyCashTransactionDetails = async (params) => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "petty_cash_reports/get_all_petty_cash",
      {
        token: token,
        user: user,
        petty_cash_id: params.petty_cash_id,
      }
    );
    return response.data;
  } catch (error) {
    return { error: error.response };
  }
};

export const searchPettyCashTransactionDetails = async (params) => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "petty_cash_reports/search",
      {
        token: token,
        requester: user,
        petty_cash_id: params.petty_cash_id,
        date_from: params.date_from
          ? Moment(params.date_from).format("YYYY-MM-DD")
          : "",
        date_to: params.date_to
          ? Moment(params.date_to).format("YYYY-MM-DD")
          : "",
        type: params.type,
        status: params.status,
      }
    );
    return response.data;
  } catch (error) {
    return { error: error.response };
  }
};

export const approvePettyCashRequest = async (id) => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "petty_cash_reports/approve_cashout",
      {
        token: token,
        requester: user,
        petty_cash_id: id,
      }
    );
    return response.data;
  } catch (error) {
    return { error: error.response };
  }
};

export const deletePettyCashTransactionDetails = async (
  petty_cash_detail_id
) => {
  try {
    const response = await postAPICall(
      process.env.REACT_APP_LINK +
        "petty_cash_reports/delete_petty_cash_detail",
      {
        petty_cash_detail_id: petty_cash_detail_id,
        token: token,
        requester: user,
      }
    );
    return response.data;
  } catch (error) {
    return { error: error.response };
  }
};

// CASH OUT

export const getPettyCashOutDetailTransaction = async (id) => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "petty_cash_reports/get_petty_cash_detail",
      {
        requester: user,
        token: token,
        petty_cash_detail_id: id,
      }
    );
    return response.data;
  } catch (error) {
    return { error: error.response };
  }
};

export const postPettyCashOutDetailTransaction = async (data) => {
  try {
    const formData = new FormData();
    if (data.attachments) {
      data.attachments.map((file) => {
        formData.append(`file[]`, file, file.name);
      });
    } else {
      formData.append(`file[]`, "");
    }
    formData.append('requester', getUser());
    formData.append('token', getToken());
    formData.append('petty_cash_id', 1);
    formData.append('type', data.type ? data.type : "");
    formData.append('out_type', data.out_type ? data.out_type : "");
    formData.append('particulars', data.particulars ? data.particulars : "");
    formData.append('invoice_no', data.invoice_no ? data.invoice_no : "");
    formData.append('date', data.date ? data.date : "");
    data.item_names.map((name) => {
      formData.append(`names[]`, name);
    }); 
    data.item_quantities.map((quantity) => {
      formData.append(`quantities[]`, quantity);
    });   
    data.item_prices.map((price) => {
      formData.append(`prices[]`, price);
    });
    data.item_units.map((unit) => {
      formData.append(`units[]`, unit);
    });
    // Ensure cash out amounts are negative (subtract from balance)
    const amount = data.amount ? -Math.abs(parseFloat(data.amount)) : 0;
    formData.append('amount', amount.toString());
    formData.append('requested_by', data.requested_by ? data.requested_by : "");
    formData.append('remarks', data.remarks ? data.remarks : "");
    

    const response = await postAPICall(
        process.env.REACT_APP_LINK + "petty_cash_reports/create_details",
        formData, 
        true
    );

    return response.data
  } catch (error) {
    return { error: error.response };
  }
}

export const updatePettyCashOutDetailTransaction = async (data) => {
  try {
    const formData = new FormData();
    if (data.attachments && data.attachments.length > 0) {
      data.attachments.forEach((file) => {
        // Safely fallback to `originFileObj` if available (e.g. from Ant Design Upload)
        const actualFile = file.originFileObj instanceof File ? file.originFileObj : file;
        if (actualFile instanceof File || actualFile instanceof Blob) {
          formData.append("file[]", actualFile, actualFile.name);
        }
      });
    } else {
      formData.append("file[]", ""); // optional fallback
    }
    formData.append('requester', getUser());
    formData.append('token', getToken());
    formData.append('petty_cash_id', 1);
    formData.append('petty_cash_detail_id', data.petty_cash_detail_id);
    formData.append('type', data.type ? data.type : "");
    formData.append('out_type', data.out_type ? data.out_type : "");
    formData.append('particulars', data.particulars ? data.particulars : "");
    formData.append('invoice_no', data.invoice_no ? data.invoice_no : "");
    formData.append('date', data.date ? data.date : "");
    data.item_names.map((name) => {
      formData.append(`names[]`, name);
    }); 
    data.item_quantities.map((quantity) => {
      formData.append(`quantities[]`, quantity);
    });   
    data.item_prices.map((price) => {
      formData.append(`prices[]`, price);
    });
    data.item_units.map((unit) => {
      formData.append(`units[]`, unit);
    });
    // Ensure cash out amounts are negative (subtract from balance)
    const amount = data.amount ? -Math.abs(parseFloat(data.amount)) : 0;
    formData.append('amount', amount.toString());
    formData.append('requested_by', data.requested_by ? data.requested_by : "");
    formData.append('remarks', data.remarks ? data.remarks : "");
    

    const response = await postAPICall(
        process.env.REACT_APP_LINK + "petty_cash_reports/update_detail",
        formData, 
        true
    );

    return response.data
  } catch (error) {
    return { error: error.response };
  }
};

// CASH IN

export const getPettyCashInDetailTransaction = async (id) => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "petty_cash_reports/get_petty_cash_detail",
      {
        requester: user,
        token: token,
        petty_cash_detail_id: id,
      }
    );
    return response.data;
  } catch (error) {
    return { error: error.response };
  }
};

export const postPettyCashInDetailTransaction = async (data) => {
  try {
    // Ensure cash in amounts are positive (add to balance)
    const amount = Math.abs(parseFloat(data.amount));

    const response = await postAPICall(
      process.env.REACT_APP_LINK + "petty_cash_reports/create_details",
      {
        requester: user,
        token: token,
        petty_cash_id: 1,
        type: data.type,
        remarks: data.remarks,
        from: data.from,
        date: data.date,
        amount: amount.toString(),
      }
    );
    return response.data;
  } catch (error) {
    return { error: error.response };
  }
};

export const updatePettyCashInDetailTransaction = async (data) => {
  try {
    // Ensure cash in amounts are positive (add to balance)
    const amount = Math.abs(parseFloat(data.amount));

    const response = await postAPICall(
      process.env.REACT_APP_LINK + "petty_cash_reports/update_detail",
      {
        requester: user,
        token: token,
        petty_cash_detail_id: data.petty_cash_detail_id,
        type: data.type,
        remarks: data.remarks,
        from: data.from,
        date: data.date,
        amount: amount.toString(),
      }
    );
    return response.data;
  } catch (error) {
    return { error: error.response };
  }
};
