// Add this CSS class for right-aligned cells
const rightAlignedCell = {
  textAlign: "right",
  paddingRight: "20px"  // Add some padding for better readability
};

// Update the table cells in the PaymentTable component
<PaymentTable
  tableHeaders={[
    "INVOICE DATE",
    "INVOICE NUMBER",
    "INVOICE AMOUNT",
    "PAID AMOUNT",
    "BALANCE",
  ]}
  headerSelector={[
    "invoice_date",
    "invoice_no",
    "grand_total",
    "paid_amount",
    "invoice_balance",
  ]}
  tableData={projectInvoiceData}
  className="text-end"  // Add this class for right alignment
/>

<table>
  <thead>
    <tr>
      <th className="custom-td text-end" style={{ fontWeight: "bold", fontSize: "20px" }}>
        Total Invoice Amount
      </th>
      <th className="custom-td text-end" style={{ fontWeight: "bold", fontSize: "20px" }}>
        Paid Amount
      </th>
      <th className="custom-td text-end" style={{ fontWeight: "bold", fontSize: "20px" }}>
        Receivable
      </th>
      <th className="custom-td text-end" style={{ fontWeight: "bold", fontSize: "20px" }}>
        Project Expense
      </th>
      <th className="custom-td text-end" style={{ fontWeight: "bold", fontSize: "20px" }}>
        Total Profit
      </th>
    </tr>
  </thead>
  <tbody>
    {projectInvoiceSummary.length > 0 ? (
      projectInvoiceSummary.map((item, index) => (
        <tr key={index}>
          <td className="custom-td text-end" style={{ fontWeight: "bold", fontSize: "20px" }}>{item.amount}</td>
          <td className="custom-td text-end" style={{ fontWeight: "bold", fontSize: "20px" }}>{item.paid_amount}</td>
          <td className="custom-td text-end" style={{ fontWeight: "bold", fontSize: "20px" }}>{item.receivable}</td>
          <td className="custom-td text-end" style={{ fontWeight: "bold", fontSize: "20px" }}>{item.project_expense}</td>
          <td className="custom-td text-end" style={{ fontWeight: "bold", fontSize: "20px" }}>{item.total_sales}</td>
        </tr>
      ))
    ) : (
      <tr>
        <td className="custom-td" colSpan="5" style={{ textAlign: "center" }}>
          No data available
        </td>
      </tr>
    )}
  </tbody>
</table> 