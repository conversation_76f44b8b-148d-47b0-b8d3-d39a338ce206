.rdt_TableHeadRow {
    font-family: var(--primary-font-bold);
    font-size: 12px !important;
    line-height: 14px !important;
    color: #5ac8e1;
    text-transform: uppercase;
    background: #3c3c43 !important;
    box-shadow: 0px 1px 7px rgba(0, 0, 0, 0.25) !important;
    border-radius: 5px;
    max-width: 100% !important;
    width: 98%;
    min-height: 7vh !important;
    height: 7vh !important;
    max-height: 30vh;
    flex: 1;
    justify-content: space-between;
    word-wrap: break-word;
    /* white-space: unset; */
    z-index: 2;
}
.fbRFLN{
    justify-content: right !important;
}
.rdt_TableHead {
    z-index: 1 !important;
}

.rdt_TableCol,
.rdt_TableCol_Sortable {
    height: 7vh;
    word-wrap: break-word;
    white-space: unset;
    max-height: 10vw;
}

.rdt_TableRow {
    font-family: var(--primary-font-medium);
    font-size: 10px;
    line-height: 14px;
    color: rgb(76, 73, 73) !important;
    text-transform: none;
    /* max-width: fit-content !important; */
    flex: 1;
    justify-content: space-between;
}


.rdt_Table {
    max-width: 98% !important;
    width: 95%;
    margin: 0vh 1vw 1vh 1vw;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    -ms-appearance: none !important;
    appearance: none !important;
    outline: none !important;
    flex-grow: inherit;
    white-space: unset;
    /* height: 100vh !important; */
    /* display: flex; */
    /* align-items: justify; */
  /* flex-wrap: nowrap; */
  /* align-items: stretch; */
}

/* .rdt_TableCol:last-child{
    max-width: 100% !important;
    margin-right: 1vw;
    text-align: left;
} */

.rdt_TableCell {
    /* min-width: 180px  !important; */
    width: 300px;
    /* max-width: max-content; */
    /* background: yellowgreen; */
    white-space: unset;
    vertical-align: top !important;
    text-align: left !important;
    padding: 8px !important;
}

/* Right alignment for total values */
td[class*="grand_total"],
td[class*="total"] {
    text-align: right !important;
    padding-right: 16px !important;
}

/* Left alignment for specific columns */
td[data-column="invoice"],
td[class*="invoice"] {
    text-align: center !important;
    vertical-align: top !important;
}

.jxflYm {
    font-family: var(--primary-font-medium) !important;
}

.rdt_Pagination {
    min-height: 6vh !important;
    height: 6vh !important;
}

.eIoOYs {
    max-height: 60vh !important;
    /* width: 98% !important; */
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    -ms-appearance: none !important;
    appearance: none !important;
    outline: none !important;
}

/* Add this to your CSS file */
.table td {
  vertical-align: top !important;
  text-align: inherit !important;
}

.text-start {
  text-align: left !important;
  vertical-align: top !important;
}

.text-wrap {
  white-space: normal !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
}

.text-break {
  word-break: break-word !important;
}

.align-top {
  vertical-align: top !important;
}

/* Add this to force table cells to respect text alignment */
td[class*="text-start"] {
  text-align: left !important;
  vertical-align: top !important;
}

td[class*="text-end"] {
  text-align: right !important;
}

/* Style for clickable invoice numbers */
.ps-label {
    text-align: center !important;
    display: block;
    cursor: pointer;
}