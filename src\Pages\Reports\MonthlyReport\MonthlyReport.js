import React, { useState } from "react";
import { Col, Row } from "react-bootstrap";
import Select from "react-select";
import Navbar from "../../../Components/Navbar/Navbar";
import Table from "../../../Components/TableTemplate/Table";
import { DatePicker } from "antd";
import {
  numberFormat,
  TokenExpiry,
  selectDropdownStyle,
  firstDayOfMonth, lastDayOfMonth,
  formatDateNoTime
} from "../../../Helpers/Utils/Common";
import { searchProject } from "./../../../Helpers/apiCalls/Manage/Projects";
import { getAllCustomer } from "./../../../Helpers/apiCalls/Manage/CustomerAPI";
import { getProjectSalesReport } from "../../../Helpers/apiCalls/SalesReport/SalesReportApi";
import { CSVLink } from "react-csv";
import ExportPdf from "../../../Components/Export/ExportPdf";
import Moment from "moment";

const {RangePicker} = DatePicker;

export default function MonthlyReport() {
  const [inactive, setInactive] = useState(true);

  const [filterConfig, setFilterConfig] = useState({
    date_from: firstDayOfMonth(),
    date_to: lastDayOfMonth(),
    payment_status: "paid",
  });

  const [showLoader, setShowLoader] = useState(false);
  const [tableData, setTableData] = useState([]);
  const [totalReceivables, setTotalReceivables] = useState(0);
  const [projects, setProjects] = useState([]);
  const [customers, setCustomers] = useState([]);

  const excelHeaders = [
    { label: "DEPOSIT DATE", key: "deposit_date" },
    { label: "INVOICE NO", key: "invoice_no" },
    { label: "PROJECT", key: "name" },
    { label: "CUSTOMER", key: "customer_name" },
    { label: "DESCRIPTION", key: "remarks" },
    { label: "PAID AMOUNT", key: "amount" },
  ];

  const pdfHeaders = [
    { label: "DEPOSIT DATE", key: "deposit_date" },
    { label: "INVOICE NO", key: "invoice_no" },
    { label: "PROJECT", key: "name" },
    { label: "CUSTOMER", key: "customer_name" },
    { label: "DESCRIPTION", key: "remarks" },
    { label: "PAID AMOUNT", key: "amount" },
  ];

  async function fetchCustomers() {
    setShowLoader(true);
    setCustomers([]);

    const response = await getAllCustomer(filterConfig);
    if (response.error) {
      TokenExpiry(response.error);
    } else {
      var projects = response.data.data.map((data) => {
        var info = {};
        info.label = data.name;
        info.value = data.id;
        return info;
      });
      setCustomers([{ label: "All Customers", value: "" }, ...projects]);
    }
    setShowLoader(false);
  }

  async function fetchProjects() {
    setProjects([]);

    const response = await searchProject();
    if (response.error) {
      if (response.error.data.status !== 404) {
        TokenExpiry(response.error.data.status);
      }
    } else {
      var projects = response.data.data.map((data) => {
        var info = {};
        info.label = data.name;
        info.value = data.id;
        return info;
      });
      setProjects([{ label: "All Projects", value: "" }, ...projects]);
    }
    setShowLoader(false);
  }

  async function fetchData() {
    setShowLoader(true);
    setTableData([]);
    setTotalReceivables(0);

    const response = await getProjectSalesReport(filterConfig);
    if (response.data) {
        let tempTotal = 0;

        const data = response.data.data?.map((data) => {
            tempTotal += parseFloat(data.paid_amount);

            let tempInvoice = "";
            if (data.project_invoice?.length > 0) {
                tempInvoice = data.project_invoice[0].invoice_no;
            }

            return {
                id: data.id, // Make sure we're getting the correct ID
                project_invoice_id: data.project_invoice?.[0]?.id, // Add project invoice ID
                remarks: data.remarks,
                invoice_no: tempInvoice,
                deposit_date: data.deposit_date ? formatDateNoTime(data.deposit_date) : '',
                raw_payment_date: data.payment_date,
                customer_name: data.customer_name,
                name: data.project_name,
                amount: numberFormat(data.paid_amount),
            };
        });

        // Sort from latest to oldest
        data.sort((a, b) => {
            if (!a.raw_payment_date) return 1; // Place empty dates last
            if (!b.raw_payment_date) return -1;
            return new Date(b.raw_payment_date) - new Date(a.raw_payment_date);
        });

        setTotalReceivables(tempTotal);
        setTableData(data);
    }

    setShowLoader(false);
}

  function handleToCSV() {
    return (
      <CSVLink
        data={tableData}
        headers={excelHeaders}
        filename={`SalesReport_${Moment().format("YYYY-MM-DD")}.csv`}
        style={{ textDecoration: "none", color: "#ffffff" }}
      >
        Export to CSV
      </CSVLink>
    );
  }
  React.useEffect(() => {
    fetchData();
  }, [filterConfig]);

  React.useEffect(() => {
    fetchCustomers();
    fetchProjects();
  }, []);

  return (
    <div>
      <div className="page">
        <Navbar
          onCollapse={(inactive) => {
            setInactive(inactive);
          }}
          active={"FINANCIAL REPORT"}
        />
      </div>

      <div className={`manager-container ${inactive ? "inactive" : "active"}`}>
        {/* headers */}
        <Row className="mb-1">
          <Col xs={6}>
            <h1 className="page-title"> CUSTOMER PAYMENTS </h1>
          </Col>
          <Col xs={6} className="d-flex justify-content-end">
            <button className="add-btn text-center">{handleToCSV()}</button>
            <ExportPdf
              name={"SalesReport"}
              data={tableData}
              header={pdfHeaders}
            ></ExportPdf>
          </Col>
        </Row>

        <div className="tab-content">
          {/* filters */}
          <div className="my-2 px-2 PO-filters d-flex">
            <Select
              className="dropsearch-filter px-0 py-0 me-2"
              classNamePrefix="react-select"
              placeholder="Select Customers"
              styles={selectDropdownStyle}
              // value={branch}
              options={customers}
              onChange={(e) => {
                setFilterConfig((prev) => {
                  return { ...prev, customer_id: e.value };
                });
              }}
            />
            <Select
              className="dropsearch-filter px-0 py-0 me-2"
              classNamePrefix="react-select"
              placeholder="Select Project"
              styles={selectDropdownStyle}
              // value={branch}
              options={projects}
              onChange={(e) => {
                setFilterConfig((prev) => {
                  return { ...prev, project_id: e.value };
                });
              }}
            />
            <RangePicker 
              placeholder={[
                filterConfig.date_from ?? firstDayOfMonth(), 
                filterConfig.date_to ?? lastDayOfMonth()
              ]} 
              onChange={(e) => {
                if (e) {
                  setFilterConfig((prev) => ({...prev,
                    date_from: e[0].format('YYYY-MM-DD'),
                    date_to: e[1].format('YYYY-MM-DD'),
                  }))
                } else {
                  setFilterConfig((prev) => ({...prev,
                    date_from: firstDayOfMonth(),
                    date_to: lastDayOfMonth(),
                  }))
                }
              }}
            />
          </div>

          <div className="my-2 px-2 PO-filters d-flex justify-content-end">
            <span className="me-4 ml-4 align-middle ps-label">
              Total Sales: {numberFormat(totalReceivables)}
            </span>
          </div>

          <div className="">
            {/* table */}
            <Table
              tableHeaders={[
                "DEPOSIT DATE",
                "INVOICE NO",
                "PROJECT",
                "CUSTOMER",
                "DESCRIPTION",
                "PAID AMOUNT",
              ]}
              headerSelector={[
                "deposit_date",
                "invoice_no",
                "name",
                "customer_name",
                "remarks",
                "amount",
              ]}
              tableData={tableData}
              onRowClicked={(row) => {
                if (row.project_invoice_id) {
                  window.open(`/projectinvoice/print/${row.project_invoice_id}`, "_blank");
                }
              }}
              showLoader={showLoader}
            />
          </div>
          <div className="mb-2" />
        </div>
      </div>
    </div>
  );
}
