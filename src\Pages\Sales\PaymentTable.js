import DataTable from "react-data-table-component";
import styled from 'styled-components';
import { SyncLoader } from "react-spinners";
import NoDataPrompt from "../../Components/NoDataPrompt/NoDataPrompt";
import "../../Components/TableTemplate/OneTable.css";
import { formatAmount, numberFormat } from "../../Helpers/Utils/Common";

export default function PaymentTable({
    tableHeaders,
    headerSelector,
    tableData,
    ActionBtn,
    ViewBtn,
    PendingBtn,
    showLoader,
    withActionData,
    handleRowClick
}) {
    const columns = tableHeaders.map((header, index) => {
        if (header === "PAYMENT DATE") {
            return {
                name: header,
                selector: (row) => row[headerSelector[index]],
                button: true,
                // width: "20%",
            };
        } else if (header.includes("TOTAL INVOICE AMOUNT"),
            header.includes("PAID AMOUNT"),
            header.includes("RECEIVABLE"),
            header.includes("PROJECT EXPENSE"),
            header.includes("TOTAL PROFIT")) {
            return {
                name: header,
                selector: (row) => row[headerSelector[index]],
                sortable: true,
                // width: '200px',
            };
        } else if (header.includes("PAID AMT") ||
            header.includes("INV AMT") ||
            header.includes("BALANCE")) {
            return {
                name: header,
                selector: (row) => row[headerSelector[index]],
                sortable: true,
                right: true,
                // width: '200px',
            };
        } else if (header.includes("DESCRIPTIONS"),
            header.includes("TYPES"),
            header.includes("PERIODS"),
            header.includes("COSTS")) {
            return {
                name: header,
                selector: (row) => row[headerSelector[index]],
                sortable: true,
                // width: '200px',
            };
        } else if (header.includes("TYPE")) {
            return {
                name: header,
                selector: (row) => row[headerSelector[index]],
                // sortable: true,
                // width: "8%",
                wrap: true,
                reorder: true,
            };
        } else if (header.includes("COST")) {
            return {
                name: header,
                selector: (row) => row[headerSelector[index]],
                // sortable: true,
                // width: "8%",
                wrap: true,
                reorder: true,
                right: true,
            };
        } else if (header.includes("TOTAL")) {
            return {
                name: header,
                selector: (row) => row[headerSelector[index]],
                // sortable: true,
                // width: "8%",
                wrap: true,
                reorder: true,
                right: true,
            };
        } else if (header?.toLowerCase().includes("amount")) {
            return {
                name: header,
                selector: (row) => row[headerSelector[index]],
                sortable: true,
                // width: "20%",
                wrap: true,
                reorder: true,
                right: true,
                cell: (row) => <span>{numberFormat(row[headerSelector[index]])}</span>
            };
        } else {
            return {
                name: header,
                selector: (row) => row[headerSelector[index]],
                // sortable: true,
                // width: "auto",
                wrap: true,
                reorder: true,
            };
        }
    });

    const paginationComponentOptions = {
        rowsPerPageText: "",
        noRowsPerPage: true,
    };

    const customStyles = {
        rows: {
            style: {
                minHeight: "5.2vh",
                flexWrap: "wrap",
                fontSize: "12px",
                whiteSpace: "pre",
            },
        },
        headCells: {
            style: {
                flexWrap: "wrap",
                fontSize: "12px",
                width: "100%",
                wordWrap: "breakWord",
            },
        },
    };

    return showLoader ? (
        <div className="d-flex justify-content-center my-5">
            <SyncLoader color="#5ac8e1" size={15} />
        </div>
    ) : (
        <DataTable
            grow
            pagination
            responsive
            striped
            fixedHeader
            columns={columns}
            data={tableData}
            customStyles={customStyles}
            paginationComponentOptions={paginationComponentOptions}
            noDataComponent={<NoDataPrompt />}
            onRowClicked={(e) => {
                if(handleRowClick){
                    handleRowClick(e)}
                }
            }
        />
    );
}
