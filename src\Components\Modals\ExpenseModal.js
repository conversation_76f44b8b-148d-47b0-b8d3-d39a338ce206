import { <PERSON><PERSON>, <PERSON><PERSON>, Form, Container, Row, Col} from "react-bootstrap";
import React, { useState } from "react";
import Table from "../../Components/TableTemplate/DataTable";
// import { render } from "react-dom";
import { 
    numberFormat,
} from "../../Helpers/Utils/Common";
import { useNavigate } from "react-router-dom";
import warning from "../../Assets/Images/Modal/warning.png";
import {searchProject} from './../../Helpers/apiCalls/Manage/Projects';
import { searchProjectExpense, deleteProjectExpense } from './../../Helpers/apiCalls/ProjectInvoice/ProjectExpenseApi';

//css
import "./Modal.css";

function ExpenseModal(props) {
    let navigate = useNavigate();
    const [filterConfig, setFilterConfig] = useState({
        name: "",
      });
    
    function handleViewBtn(id, status) {
        navigate("/projectexpense/view/" + id + "/" + status);
    }
    function handleSelectChange(e, id) {
   
    if (e.target.value === "view-invoice") {
      window.open("projectexpense/view/" + id +"/approved","_blank");
    }
  }
    function ViewBtn(row) {
      return (
        <button
          name="action"
          className="btn btn-sm view-btn-table"
          id={row.id}
          onClick={(e) =>
            handleSelectChange(e, row.id)
          }
          value="view-invoice"
        >
          View
        </button>
      );
    }
    
    // async function fetchData() {
    //     setShowLoader(true);
    //     setAllData([]);
    
    //     const response = await searchProjectExpense(filterConfig);
    //     if (response.data) {
    
    //       var allData = response.data.data.map((data) => {
    //         var info = data;
    //         info.added_on = data.added_on !== "0000-00-00" ? formatDateNoTime(data.added_on) : "";
    //         info.amount = numberFormat(data.amount);
    //         info.balance = numberFormat(data.balance);
    //         info.paid_amount = data.paid_amount ? numberFormat(data.paid_amount) : "0.00";
    //         return info;
    //       });
    //       setAllData(allData);
    
    //     } else if (response.error) {
    //       if(response.error.data.status !== 404) {
    //         TokenExpiry(response.error);
    //       }
    //     }
    //     setShowLoader(false);
    //   }
    return (
    <div>
      <Modal
        show={props.show}
        onHide={props.onHide}
        size={props.size}
        centered
      >
      <Modal.Header/>
        <Modal.Body>
          <div className="col-sm-12">
            <Row>
              <Col sm={7}>
                <Row>
                  <span className="expense-modal-body-title">{props.data[0] ? props.data[0].name : ""} Expense Breakdown</span>
                </Row>
              </Col>
              <Col sm={5}>
                 <Row>
                    <span className="expense-modal-body-title-right">Total: {props.sales ? props.sales.project_expense : ""}</span>
                 </Row>
              </Col>
            </Row>
            <Container fluid className="modal-cont justify-content-center">
                <div className="sales-tbl">
                            <Table
                                tableHeaders={[
                                    "PROJECT NAME",
                                    "START DATE",
                                    "CUSTOMER",
                                    "AMOUNT",
                                    "PAID AMOUNT",
                                    "BALANCE",
                                    "PROJECT EXPENSE",
                                    "TOTAL PROFIT",
                                ]}
                                headerSelector={[
                                    "PROJECT NAME",
                                    "START DATE",
                                    "CUSTOMER",
                                    "AMOUNT",
                                    "PAID AMOUNT",
                                    "BALANCE",
                                    "PROJECT EXPENSE",
                                    "TOTAL PROFIT",
                                ]}
                                tableData={props.data}
                                // showLoader={showLoader}
                                ViewBtn={(row) => ViewBtn(row)}
                                withActionData={false}
                            />
                        </div>
                
            </Container>
         
            <div className="col-sm-12 mt-3 d-flex justify-content-end">
                <button className="button-tertiary mr-3" onClick={props.onHide}>
                    Close
                </button>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </div>
  );
}

ExpenseModal.defaultProps = {
  title:"",
  type:"",
  size:"xl",
  withButtons: false,
  withHeader: false,
  show:()=>{},
  onHide:()=>{},
  onEdit:()=>{}
}

export default ExpenseModal;