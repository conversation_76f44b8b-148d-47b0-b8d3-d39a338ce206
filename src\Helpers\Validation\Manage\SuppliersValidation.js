import {handleValidationChange} from '../CommonValidation';

export const validateSuppliers = (data, setIsError) => {

    var isValid = true;

    if(data.trade_name === "" || data.trade_name === null) {
        handleValidationChange("trade_name", true, setIsError);
        isValid=false;
    } else {
        handleValidationChange("trade_name", false, setIsError);
    }

    if(data.trade_address === "" || data.trade_address === null) {
        handleValidationChange("trade_address", true, setIsError);
        isValid=false;
    } else {
        handleValidationChange("trade_address", false, setIsError);
    }

    if(data.vat_type === "" || data.vat_type === null) {
        handleValidationChange("vat_type", true, setIsError);
        isValid=false;
    } else {
        handleValidationChange("vat_type", false, setIsError);
    }

    // if(data.contact_person === "" || data.contact_person === null) {
    //     handleValidationChange("contact_person", true, setIsError);
    //     isValid=false;
    // } else {
    //     handleValidationChange("contact_person", false, setIsError);
    // }

    // if(data.phone_no === "" || data.phone_no === null) {
    //     handleValidationChange("phone_no", true, setIsError);
    //     isValid=false;
    // } else {
    //     handleValidationChange("phone_no", false, setIsError);
    // }

    // if(data.payee === "" || data.payee === null) {
    //     handleValidationChange("payee", true, setIsError);
    //     isValid=false;
    // } else {
    //     handleValidationChange("payee", false, setIsError);
    // }

    return isValid;
}
