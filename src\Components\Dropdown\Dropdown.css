body {
  background: #fcfcfc;
}

.dropdown {
  font-family: var(--primary-font-medium);
  /*padding: 3%;*/
  cursor: pointer;
  user-select: none;
  position: relative;
}

.dropdown .dropdown-btn {
  padding: 1px 15px;
  background: #5ac8e1;
  box-sizing: border-box;
  border-radius: 5px !important;
  font-family: var(--primary-font-medium);
  font-weight: lighter;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.caret-down {
  margin-left: 10px;
}

.dropdown .dropdown-content {
  background: #ffffff;
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.25) !important;
  font-family: var(--primary-font-medium);
  font-size: 12px;
  font-weight: 100;
  color: rgb(255, 255, 255);
  z-index: 10;
  position: absolute;
  width: 100%;
  border-radius: 5px !important;
}

.dropdwn .dropdown-content .dropdown-item {
  padding: 10px;
  border-radius: 5px !important;

}

.dropdown .dropdown-content .dropdown-item:hover {
  background: #e5ffe0;
}

.dropdown:hover .dropdown-content {
  display: block;
}

.blue {
  background-color: #74ecc9 !important;
}

.yellow {
  color: #3c3c43 !important;
}

.green {
  background-color: #5ac8e1 !important;
}

.red {
  color: #DC3545 !important;
}