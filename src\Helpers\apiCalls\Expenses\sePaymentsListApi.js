import { fi } from "date-fns/locale";
import { getToken, getUser } from "../../Utils/Common";
import { getAPICall, postAPICall } from "../axiosMethodCalls";
import Moment from "moment";

//GET MYT FOUNDATION
// export const getAllSePayments = async (datefrom, dateto) => {
//     try {
//         const response = await getAPICall(
//             process.env.REACT_APP_LINK + "supplies_expense_payments/get_all_payment",
//             {
//                 requester: getUser(),
//                 token: getToken(),
//                 // status: status,
//                 start_date: datefrom,
//                 end_date: dateto,
//             }
//         );
//         return { data: response.data };
//     } catch (error) {
//         return { error: error.response };
//     }
// };

// GET NGROK
export const getAllSePayments = async (data) => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "se_payments/get_all_payment",
      {
        requester: getUser(),
        token: getToken(),
        // status: status,
        start_date: data.start_date
          ? Moment(data.start_date).format("YYYY-MM-DD")
          : "",
        end_date: data.end_date
          ? Moment(data.end_date).format("YYYY-MM-DD")
          : data.end_date,
        supplier_id: data.supplier_id,
        vendor_id: data.vendor_id,
        payment_mode: data.payment_mode,
        doc_no: data.doc_no,
      }
    );
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};

//FILTER
export const searchByDate = async (data) => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "se_payments/get_all_payment",
      {
        requester: getUser(),
        token: getToken(),
        start_date: Moment(data.from).format("YYYY-MM-DD"),
        end_date: data.to ? Moment(data.to).format("YYYY-MM-DD") : data.to,
        supplier_id: data.supplier_id,
        payment_mode: data.payment_mode,
      }
    );
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};

// Add this helper function
const formatAttachments = (attachments) => {
  if (!attachments || !Array.isArray(attachments)) return [];
  
  return attachments.map((file, index) => ({
    uid: `existing-${file.id}`,
    name: file.file_name,
    status: 'done',
    url: file.file_url,
    type: file.mime,
    thumbUrl: file.file_url,
    response: { status: 'success' }
  }));
};

//GET SINGLE CASH
export const getSingleCashSe = async (id) => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "SE_cash_payments/get_slip",
      {
        requester: getUser(),
        token: getToken(),
        slip_id: id,
      }
    );
    
    if (response.data?.status === "success" && response.data?.data?.[0]) {
      const data = response.data.data[0];
      // Format the attachments
      data.attachments = formatAttachments(data.cash_slip_attachments);
    }
    
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};

//GET SINGLE GCASH 
export const getSingleGcashSe = async (id) => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "SE_gcash_payments/get_slip",
      {
        requester: getUser(),
        token: getToken(),
        slip_id: id,
      }
    );
    
    if (response.data?.status === "success" && response.data?.data?.[0]) {
      const data = response.data.data[0];
      // Format the attachments
      data.attachments = formatAttachments(data.gcash_slip_attachments);
    }
    
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};

//GET SINGLE CHECK
export const getSingleCheckSe = async (id) => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "SE_check_payments/get_slip",
      {
        requester: getUser(),
        token: getToken(),
        slip_id: id,
      }
    );
    
    if (response.data?.status === "success" && response.data?.data?.[0]) {
      const data = response.data.data[0];
      // Format the attachments  
      data.attachments = formatAttachments(data.check_slip_attachments);
    }
    
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};

//GET SINGLE BANK
export const getSingleBankSe = async (id) => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "SE_bank_payments/get_slip",
      {
        requester: getUser(),
        token: getToken(),
        slip_id: id,
      }
    );
    
    if (response.data?.status === "success" && response.data?.data?.[0]) {
      const data = response.data.data[0];
      // Format the attachments
      data.attachments = formatAttachments(data.bank_slip_attachments);
    }
    
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};

//CREATE CHECK PAYMENT
export const createSeCheckPayment = async (data) => {
  try {
    const formData = new FormData();

    // Append files
    if (data.attachments && data.attachments.length > 0) {
      data.attachments.forEach((file) => {
        formData.append('attachments[]', file, file.name);
      });
    }

    // Append basic info
    formData.append('requester', getUser());
    formData.append('token', getToken());

    // Append payment details
    formData.append('supplier_id', data.supplier_id || "");
    formData.append('vendor_id', data.vendor_id || "");
    formData.append('bank_id', data.bank_id || "");
    formData.append('check_no', data.check_no || "");
    formData.append('check_date', data.check_date || "");
    formData.append('issued_date', data.issued_date || "");
    formData.append('payee', data.payee || "");
    formData.append('particulars', data.particulars || "");
    formData.append('acknowledged_by', data.acknowledged_by || "");

    // Append items data
    if (data.items) {
      data.items.forEach((item, index) => {
        formData.append(`se_ids[${index}]`, item.entry.value);
        formData.append(`amounts[${index}]`, item.amount);
        formData.append(`types[${index}]`, item.type);
      });
    }

    const response = await postAPICall(
      process.env.REACT_APP_LINK + "SE_check_payments/create",
      formData,
      true
    );
    return { data: response.data };
  } catch (error) {
    return { error: error };
  }
};

// CREATE CASH PAYMENT
export const createSeCashPayment = async (data) => {
  try {
    const formData = new FormData();

    if (data.attachments && data.attachments.length > 0) {
      data.attachments.forEach((file) => {
        formData.append('attachments[]', file, file.name);
      });
    }
    
    // Append basic info
    formData.append('requester', getUser());
    formData.append('token', getToken());

    // Append payment details 
    formData.append('payment_date', data.payment_date || "");
    formData.append('supplier_id', data.supplier_id || "");
    formData.append('vendor_id', data.vendor_id || "");
    formData.append('payee', data.payee || "");
    formData.append('acknowledged_by', data.acknowledged_by || "");
    formData.append('particulars', data.particulars || "");

    // Append items data
    if (data.items) {
      data.items.forEach((item, index) => {
        formData.append(`se_ids[${index}]`, item.entry.value);
        formData.append(`amounts[${index}]`, item.amount);
        formData.append(`types[${index}]`, item.type);
      });
    }

    const response = await postAPICall(
      process.env.REACT_APP_LINK + "SE_cash_payments/create",
      formData,
      true
    );
    return { data: response.data };
  } catch (error) {
    return { error: error };
  }
};

// CREATE GCASH PAYMENT
export const createSeGcashPayment = async (data) => {
  try {
    const formData = new FormData();

    // Append files
    if (data.attachments && data.attachments.length > 0) {
      data.attachments.forEach((file) => {
        formData.append('attachments[]', file, file.name);
      });
    }

    // Append basic info
    formData.append('requester', getUser());
    formData.append('token', getToken());

    // Append payment details
    formData.append('payment_date', data.payment_date || "");
    formData.append('supplier_id', data.supplier_id || "");
    formData.append('vendor_id', data.vendor_id || "");
    formData.append('payee', data.payee || "");
    formData.append('account_no', data.account_no || "");
    formData.append('account_name', data.account_name || "");
    formData.append('acknowledged_by', data.acknowledged_by || "");
    formData.append('particulars', data.particulars || "");
    formData.append('reference_no', data.reference_no || "");

    // Append items data
    if (data.items) {
      data.items.forEach((item, index) => {
        formData.append(`se_ids[${index}]`, item.entry.value);
        formData.append(`amounts[${index}]`, item.amount);
        formData.append(`types[${index}]`, item.type);
      });
    }

    const response = await postAPICall(
      process.env.REACT_APP_LINK + "SE_gcash_payments/create",
      formData,
      true
    );
    return { data: response.data };
  } catch (error) {
    return { error: error };
  }
};

// CREATE BANK PAYMENT
export const createSeBankPayment = async (data) => {
  try {
    const formData = new FormData();

    // // Append files
    if (data.attachments && data.attachments.length > 0) {
      data.attachments.forEach((file) => {
        formData.append('attachments[]', file, file.name);
      });
    }

    // Append basic info
    formData.append('requester', getUser());
    formData.append('token', getToken());

    // Append payment details
    formData.append('payment_date', data.payment_date || "");
    formData.append('bank_from', data.bank_from || "");
    formData.append('from_account_no', data.from_account_no || "");
    formData.append('from_account_name', data.from_account_name || "");
    formData.append('bank_to', data.bank_to || "");
    formData.append('to_account_no', data.to_account_no || "");
    formData.append('to_account_name', data.to_account_name || "");
    formData.append('transaction_fee', data.transaction_fee || 0);
    formData.append('reference_no', data.reference_no || "");
    formData.append('supplier_id', data.supplier_id || "");
    formData.append('vendor_id', data.vendor_id || "");
    formData.append('payee', data.payee || "");
    formData.append('acknowledged_by', data.acknowledged_by || "");
    formData.append('particulars', data.particulars || "");

    // Append items data
    if (data.items) {
      data.items.forEach((item, index) => {
        formData.append(`se_ids[${index}]`, item.entry.value);
        formData.append(`amounts[${index}]`, item.amount);
        formData.append(`types[${index}]`, item.type);
      });
    }

    const response = await postAPICall(
      process.env.REACT_APP_LINK + "SE_bank_payments/create",
      formData,
      true
    );
    return { data: response.data };
  } catch (error) {
    return { error: error };
  }
};

// UPDATE CHECK PAYMENT
export const updateCheckPayment = async (data, items, id) => {
  try {
    const formData = new FormData();

    // Append files
    if (data.attachments && data.attachments.length > 0) {
      data.attachments.forEach((file) => {
        formData.append('attachments[]', file, file.name);
      });
    }

    // Append basic info
    formData.append('requester', getUser());
    formData.append('token', getToken());
    formData.append('se_check_slip_id', id);

    // Append payment details
    formData.append('supplier_id', data.supplier_id || "");
    formData.append('vendor_id', data.vendor_id || "");
    formData.append('bank_id', data.bank_id || "");
    formData.append('check_no', data.check_no || "");
    formData.append('check_date', data.check_date || "");
    formData.append('issued_date', data.issued_date || "");
    formData.append('payee', data.payee || "");
    formData.append('particulars', data.particulars || "");
    formData.append('acknowledged_by', data.acknowledged_by || "");

    // Append items data
    if (items) {
      items.forEach((item, index) => {
        formData.append(`se_ids[${index}]`, item.entry.value);
        formData.append(`amounts[${index}]`, item.amount);
      });
    }

    const response = await postAPICall(
      process.env.REACT_APP_LINK + "SE_check_payments/update",
      formData,
      true
    );
    return { data: response.data };
  } catch (error) {
    return { error: error };
  }
};

// UPDATE GCASH PAYMENT
export const updateGcashPayment = async (data, items, id) => {
  try {
    const formData = new FormData();

    // Append files
    if (data.attachments && data.attachments.length > 0) {
      data.attachments.forEach((file) => {
        formData.append('attachments[]', file, file.name);
      });
    }

    // Append basic info
    formData.append('requester', getUser());
    formData.append('token', getToken());
    formData.append('se_gcash_slip_id', id);

    // Append payment details
    formData.append('payment_date', data.payment_date || "");
    formData.append('supplier_id', data.supplier_id || "");
    formData.append('vendor_id', data.vendor_id || "");
    formData.append('payee', data.payee || "");
    formData.append('account_no', data.account_no || "");
    formData.append('account_name', data.account_name || "");
    formData.append('reference_no', data.reference_no || "");
    formData.append('particulars', data.particulars || "");
    formData.append('acknowledged_by', data.acknowledged_by || "");

    // Append items data
    if (items) {
      items.forEach((item, index) => {
        formData.append(`se_ids[${index}]`, item.entry.value);
        formData.append(`amounts[${index}]`, item.amount);
      });
    }

    const response = await postAPICall(
      process.env.REACT_APP_LINK + "SE_gcash_payments/update",
      formData,
      true
    );
    return { data: response.data };
  } catch (error) {
    return { error: error };
  }
};

// UPDATE CASH PAYMENT
export const updateCashPayment = async (data, items, id) => {
  try {
    const formData = new FormData();

    // Append files
    if (data.attachments && data.attachments.length > 0) {
      data.attachments.forEach((file) => {
        formData.append('attachments[]', file, file.name);
      });
    }

    // Append basic info
    formData.append('requester', getUser());
    formData.append('token', getToken());
    formData.append('se_cash_slip_id', id);

    // Append payment details
    formData.append('payment_date', data.payment_date || "");
    formData.append('supplier_id', data.supplier_id || "");
    formData.append('vendor_id', data.vendor_id || "");
    formData.append('payee', data.payee || "");
    formData.append('particulars', data.particulars || "");
    formData.append('acknowledged_by', data.acknowledged_by || "");

    // Append items data
    if (items) {
      items.forEach((item, index) => {
        formData.append(`se_ids[${index}]`, item.entry.value);
        formData.append(`amounts[${index}]`, item.amount);
      });
    }

    const response = await postAPICall(
      process.env.REACT_APP_LINK + "SE_cash_payments/update",
      formData,
      true
    );
    return { data: response.data };
  } catch (error) {
    return { error: error };
  }
};

// UPDATE BANK PAYMENT
export const updateBankPayment = async (data, items, transactionFee, id) => {
  try {
    const formData = new FormData();

    // Append files
    if (data.attachments && data.attachments.length > 0) {
      data.attachments.forEach((file) => {
        formData.append('attachments[]', file, file.name);
      });
    }

    // Append basic info
    formData.append('requester', getUser());
    formData.append('token', getToken());
    formData.append('se_bank_slip_id', id);

    // Append payment details
    formData.append('payment_date', data.payment_date || "");
    formData.append('bank_from', data.bank_from || "");
    formData.append('from_account_no', data.from_account_no || "");
    formData.append('from_account_name', data.from_account_name || "");
    formData.append('bank_to', data.bank_to || "");
    formData.append('to_account_no', data.to_account_no || "");
    formData.append('to_account_name', data.to_account_name || "");
    formData.append('transaction_fee', transactionFee || 0);
    formData.append('reference_no', data.reference_no || "");
    formData.append('supplier_id', data.supplier_id || "");
    formData.append('vendor_id', data.vendor_id || "");
    formData.append('payee', data.payee || "");
    formData.append('acknowledged_by', data.acknowledged_by || "");
    formData.append('particulars', data.particulars || "");

    // Append items data
    if (items) {
      items.forEach((item, index) => {
        formData.append(`se_ids[${index}]`, item.entry.value);
        formData.append(`amounts[${index}]`, item.amount);
      });
    }

    const response = await postAPICall(
      process.env.REACT_APP_LINK + "SE_bank_payments/update",
      formData,
      true
    );
    return { data: response.data };
  } catch (error) {
    return { error: error };
  }
};

// DELETE CHECK
export const deleteCheckSe = async (id) => {
  try {
    var payload = {
      requester: getUser(),
      token: getToken(),
      se_check_slip_id: id,
    };
    const response = await postAPICall(
      process.env.REACT_APP_LINK + "SE_check_payments/delete_slip",
      payload
    );
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};
// DELETE CASH
export const deleteCashSe = async (id) => {
  try {
    var payload = {
      requester: getUser(),
      token: getToken(),
      se_cash_slip_id: id,
    };
    const response = await postAPICall(
      process.env.REACT_APP_LINK + "SE_cash_payments/delete_slip",
      payload
    );
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};
// DELETE Bank
export const deleteBankSe = async (id) => {
  try {
    var payload = {
      requester: getUser(),
      token: getToken(),
      se_bank_slip_id: id,
    };
    const response = await postAPICall(
      process.env.REACT_APP_LINK + "SE_bank_payments/delete_slip",
      payload
    );
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};
