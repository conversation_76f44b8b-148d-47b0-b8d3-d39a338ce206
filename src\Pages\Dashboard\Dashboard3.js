import React, { useState, useEffect, Fragment } from "react";
import { Row, Col } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import { Badge } from "antd";
import SalesImg from "../../Assets/Images/Dashboard/sales.png";
import ExpensesImg from "../../Assets/Images/Dashboard/expenses.png";
import ReceivablesImg from "../../Assets/Images/Dashboard/receivables.png";
import NetsalesImg from "../../Assets/Images/Dashboard/netsales.png";
import moment from "moment";
import Navbar from "../../Components/Navbar/Navbar";

import "../../Components/Navbar/Navbar.css";
import "./Dashboard.css";
import {  
  formatDateNoTime,
  getName,
  getTime,
  getType,
  numberFormat,
  TokenExpiry,
} from "../../Helpers/Utils/Common";
import { getDashboardReport } from "../../Helpers/apiCalls/DashboardReport/DashboardReportApi";

export default function Dashboard3() {
  let navigate = useNavigate();
  const type = getType();

  const [data, setData] = useState({
    sales: 0,
    expenses: 0,
    net_sales: 0,
    receivables: 0,
    pending_invoice: 0,
    pending_expense: 0,
    projects_to_bill: 0,
    for_approval_po: 0,
    unpaid_po: 0,
    for_approval_project_expense: 0,
    petty_cash: 0,
    open_invoice: 0,
  });

  const [inactive, setInactive] = useState(false);
  const days = {
    0: "Sunday",
    1: "Monday",
    2: "Tuesday",
    3: "Wednesday",
    4: "Thursday",
    5: "Friday",
    6: "Saturday",
  };

  // Dashboard Data
  async function fetchData() {
    const response = await getDashboardReport();

    if (response.data) {
      setData(response.data.data);
    } else if (response.error) {
      TokenExpiry(response);
    }
  }

  useEffect(() => {
    fetchData();
    // fetchOpenBranches();
  }, []);

  return (
    <div className="dashboard-wrapper justify-content-evenly">
      <div className="page">
        <Navbar
          onCollapse={(inactive) => {
            setInactive(inactive);
          }}
          active={"DASHBOARD"} //Dashboard navbar index
        />
      </div>
      <div className={`dashboard ${inactive ? "inactive" : "active"}`}>
        <div className={"dashboard-wrapper"}>
          <Row className="d-flex justify-content-between align-items-end">
            <Col xs={"auto"}>
              <h2 className="font-medium">
                Hello, <b>{getName()}!</b>👋
              </h2>
            </Col>
            <Col xs={"auto"}>
              <h3 className="date-and-time">{`${getTime(new Date())} ${
                days[new Date().getDay()]
              } | ${formatDateNoTime(new Date())}`}</h3>
            </Col>
          </Row>
          {type === "admin" ? (
            <Fragment>
              <Row className="d-flex">
                <Col className="mt-5">
                  <Row>
                    <Col className="box-1 d-flex justify-content-center align-items-center mx-2 cursor">
                      <div className="me-2 justify-content-center">
                        <img
                          src={SalesImg}
                          className="print-img"
                          style={{ width: "60px", height: "60px" }}
                          alt=""
                        />
                      </div>
                      <Row>
                        <div
                          className="big-hdr"
                          onClick={(e) => navigate("/projectsales")}
                        >
                          Sales
                        </div>
                        <div
                          className="stats"
                          onClick={(e) => navigate("/projectsales")}
                        >
                          PHP{numberFormat(data?.sales ?? '0')}
                        </div>
                        <div className="box-low-text">for the month</div>
                      </Row>
                    </Col>
                    <Col className="box-1 d-flex justify-content-center align-items-center mx-2 cursor">
                      <div className="me-2">
                        <img
                          src={ExpensesImg}
                          className="print-img"
                          style={{ width: "60px", height: "60px" }}
                          alt=""
                        />
                      </div>
                      <Row className="justify-content">
                        <div
                          className="big-hdr"
                          onClick={(e) =>
                            navigate(
                              "/expensereport/" +
                                moment().format("YYYY-MM-DD") +
                                "/" +
                                moment().format("YYYY-MM-DD")
                            )
                          }
                        >
                          Expenses
                        </div>
                        <div
                          className="stats"
                          onClick={(e) =>
                            navigate(
                              "/expensereport/" +
                                moment().format("YYYY-MM-DD") +
                                "/" +
                                moment().format("YYYY-MM-DD")
                            )
                          }
                        >
                          PHP{numberFormat(data.expenses ?? '0')}
                        </div>
                        <div className="box-low-text">for the month</div>
                      </Row>
                    </Col>
                    <Col className="box-1 d-flex justify-content-center align-items-center mx-2 cursor">
                      <div className="me-2">
                        <img
                          src={ReceivablesImg}
                          className="print-img"
                          style={{ width: "60px", height: "60px" }}
                          alt=""
                        />
                      </div>
                      <Row>
                        <div
                          className="big-hdr"
                          onClick={(e) => navigate("/receivablesagingreport")}
                        >
                          Receivables
                        </div>
                        <div
                          className="stats"
                          onClick={(e) => navigate("/receivablesagingreport")}
                        >
                          PHP{numberFormat(data.receivables ?? '0')}
                        </div>
                        <div className="box-low-text">for the month</div>
                      </Row>
                    </Col>
                    <Col className="box-1 d-flex justify-content-center align-items-center mx-2">
                      <div className="me-2">
                        <img
                          src={NetsalesImg}
                          className="print-img"
                          style={{ width: "60px", height: "60px" }}
                          alt=""
                        />
                      </div>
                      <Row>
                        <div
                          className="big-hdr"
                          // onClick={(e) => navigate("/netsales")}
                        >
                          Net Sales
                        </div>
                        <div
                          className="stats"
                          // onClick={(e) => navigate("/netsales")}
                        >
                          PHP
                          {numberFormat(data.net_sales ?? '0')}
                        </div>
                        <div className="box-low-text">for the month</div>
                      </Row>
                    </Col>
                  </Row>
                </Col>
              </Row>
              <Row className="d-flex">
                <Col className="mt-3">
                  <Row>
                    <Col className="box-1 mx-2">
                      <div className="me-2"></div>
                      <Row>
                        <div
                          className="header cursor"
                          onClick={(e) => navigate("/suppliesexpenses?tab=for_approval")}
                        >
                          PO for Approval
                        </div>
                        <div
                          className="box-text cursor"
                          onClick={(e) => navigate("/suppliesexpenses?tab=for_approval")}
                        >
                          {data.for_approval_po ?? '0'}
                        </div>
                        <div className="box-low-text"> Total Number</div>
                      </Row>
                    </Col>
                    <Col className="box-1 mx-2">
                      <div className="me-2"></div>
                      <Row>
                        <div
                          className="header cursor"
                          onClick={(e) => navigate("/suppliesexpenses?tab=incomplete")}
                        >
                          Unpaid PO
                        </div>
                        <div
                          className="box-text cursor"
                          onClick={(e) => navigate("/suppliesexpenses?tab=incomplete")}
                        >
                          {data.unpaid_po ?? '0'}
                        </div>
                        <div className="box-low-text"> Total Number</div>
                      </Row>
                    </Col>
                    <Col className="box-1 mx-2">
                      <div className="me-2"></div>
                      <Row>
                        <div
                          className="header cursor"
                          onClick={(e) => navigate("/projectexpense")}
                        >
                          For Approval Project Expense
                        </div>
                        <div
                          className="box-text cursor"
                          onClick={(e) => navigate("/projectexpense")}
                        >
                          {data.for_approval_project_expense ?? '0'}
                        </div>
                        <div className="box-low-text"> Total Number</div>
                      </Row>
                    </Col>
                    <Col className="box-1 mx-2">
                      <div className="me-2"></div>
                      <Row>
                        <div
                          className="header cursor"
                          onClick={(e) => navigate("/pettycash?tab=request")}
                        >
                          Petty Cash
                        </div>
                        <div
                          className="box-text cursor"
                          onClick={(e) => navigate("/pettycash?tab=request")}
                        >
                          {data.petty_cash ?? '0'}
                        </div>
                        <div className="box-low-text"> Total Number</div>
                      </Row>
                    </Col>
                  </Row>
                </Col>
              </Row>
              <Row className="d-flex">
                <Col className="mt-3">
                  <Row>
                    <Col className="box-1 mx-2">
                      <div className="me-2"></div>
                      <Row>
                        <div
                          className="header cursor red-text"
                          onClick={(e) => navigate("/forbilling")}
                        >
                          For Billing
                        </div>
                        <div
                          className="box-text cursor red-text"
                          onClick={(e) => navigate("/forbilling")}
                        >
                          {data.projects_to_bill ?? '0'}
                        </div>
                        <div className="box-low-text red-text"> Total Number</div>
                      </Row>
                    </Col>
                    <Col className="box-1 mx-2">
                      <div className="me-2"></div>
                      <Row>
                        <div
                          className="header cursor"
                          onClick={(e) => navigate("/projectinvoice?tab=pending")}
                        >
                          Pending Invoice
                        </div>
                        <div
                          className="box-text cursor"
                          onClick={(e) => navigate("/projectinvoice?tab=pending")}
                        >
                          {data.pending_invoice ?? '0'}
                        </div>
                        <div className="box-low-text"> Total Number</div>
                      </Row>
                    </Col>
                    <Col className="box-1 mx-2">
                      <div className="me-2"></div>
                      <Row>
                        <div
                          className="header cursor"
                          onClick={(e) => navigate("/projectinvoice?tab=open_bill")}
                        >
                          Open Invoice
                        </div>
                        <div
                          className="box-text cursor"
                          onClick={(e) => navigate("/projectinvoice?tab=open_bill")}
                        >
                          {data.open_invoice ?? '0'}
                        </div>
                        <div className="box-low-text"> Total Number</div>
                      </Row>
                    </Col>
                  </Row>
                </Col>
              </Row>
            </Fragment>
          ) : (
            /* Content for non-admin users */
            <div>Content for non-admin users</div>
          )}
        </div>
      </div>
    </div>
  );
}
