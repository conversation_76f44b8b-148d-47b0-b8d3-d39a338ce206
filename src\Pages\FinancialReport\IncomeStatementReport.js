import React, { useContext, useEffect, useState } from "react"
import { Col, Row } from "react-bootstrap"
import { DatePicker, Select, Table as AntTable } from "antd"
import Navbar from "../../Components/Navbar/Navbar"
import {
  formatAmount,
  numberFormat,
  toastStyle,
} from "../../Helpers/Utils/Common"
import { getFinancialReport } from "../../Helpers/apiCalls/SalesReport/SalesReportApi"
import dayjs from "dayjs"
import Table from "../../Components/TableTemplate/Table"
import { CSVLink } from "react-csv" // Import CSVLink
import Moment from "moment"
import downloadIcon from "../../Assets/Images/download_icon.png" // Import the download icon
import ExpensesBreakdownModal from "../../Components/Modals/ExpensesBreakdownModal" // Import the modal

const { RangePicker } = DatePicker
export default function BalanceSheet() {
  const [inactive, setInactive] = useState(true)
  const [filterConfig, setFilterConfig] = useState({
    date_from: dayjs().startOf("month").format("YYYY-MM-DD"),
    date_to: dayjs().endOf("month").format("YYYY-MM-DD"),
    year: dayjs().year(),
  })
  const [showLoader, setShowLoader] = useState(false)
  const [tableData, setTableData] = useState([])
  const [showModal, setShowModal] = useState(false) // State to control modal visibility
  const [modalData, setModalData] = useState([]) // State to hold modal data

  const excelHeaders = [
    { label: "Type", key: "name" },
    { label: "Jan", key: "jan" },
    { label: "Feb", key: "feb" },
    { label: "Mar", key: "mar" },
    { label: "Apr", key: "apr" },
    { label: "May", key: "may" },
    { label: "Jun", key: "jun" },
    { label: "Jul", key: "jul" },
    { label: "Aug", key: "aug" },
    { label: "Sep", key: "sep" },
    { label: "Oct", key: "oct" },
    { label: "Nov", key: "nov" },
    { label: "Dec", key: "dec" },
    { label: "Total", key: "total" },
  ]

  function handleToCSV() {
    return (
      <CSVLink
        data={tableData}
        headers={excelHeaders}
        filename={`IncomeStatementReport_${Moment().format("YYYY-MM-DD")}.csv`}
        style={{ textDecoration: "none", color: "#ffffff" }}
      >
        Export to CSV
      </CSVLink>
    )
  }

  async function fetchTableData() {
    setShowLoader(true)
    setTableData([])

    const response = await getFinancialReport(filterConfig)

    if (response.data) {
      const formattedData = response.data.account_types.map((asset) => {
        const baseData = {
          name: asset.name.toUpperCase(),
          key: asset.name.toUpperCase(),
          jan: asset.jan ? numberFormat(asset.jan.toFixed(2)) : "0.00",
          feb: asset.feb ? numberFormat(asset.feb.toFixed(2)) : "0.00",
          mar: asset.mar ? numberFormat(asset.mar.toFixed(2)) : "0.00",
          apr: asset.apr ? numberFormat(asset.apr.toFixed(2)) : "0.00",
          may: asset.may ? numberFormat(asset.may.toFixed(2)) : "0.00",
          jun: asset.jun ? numberFormat(asset.jun.toFixed(2)) : "0.00",
          jul: asset.jul ? numberFormat(asset.jul.toFixed(2)) : "0.00",
          aug: asset.aug ? numberFormat(asset.aug.toFixed(2)) : "0.00",
          sep: asset.sep ? numberFormat(asset.sep.toFixed(2)) : "0.00",
          oct: asset.oct ? numberFormat(asset.oct.toFixed(2)) : "0.00",
          nov: asset.nov ? numberFormat(asset.nov.toFixed(2)) : "0.00",
          dec: asset.dec ? numberFormat(asset.dec.toFixed(2)) : "0.00",
          total: numberFormat(asset.total_amount?.toFixed(2)),
          children: asset?.children?.map((item) => ({
            ...item,
            expense_type_id: item.id,
            jan: item.jan ? numberFormat(item.jan.toFixed(2)) : "0.00",
            feb: item.feb ? numberFormat(item.feb.toFixed(2)) : "0.00",
            mar: item.mar ? numberFormat(item.mar.toFixed(2)) : "0.00",
            apr: item.apr ? numberFormat(item.apr.toFixed(2)) : "0.00",
            may: item.may ? numberFormat(item.may.toFixed(2)) : "0.00",
            jun: item.jun ? numberFormat(item.jun.toFixed(2)) : "0.00",
            jul: item.jul ? numberFormat(item.jul.toFixed(2)) : "0.00",
            aug: item.aug ? numberFormat(item.aug.toFixed(2)) : "0.00",
            sep: item.sep ? numberFormat(item.sep.toFixed(2)) : "0.00",
            oct: item.oct ? numberFormat(item.oct.toFixed(2)) : "0.00",
            nov: item.nov ? numberFormat(item.nov.toFixed(2)) : "0.00",
            dec: item.dec ? numberFormat(item.dec.toFixed(2)) : "0.00",
            total: numberFormat(item.total_amount?.toFixed(2)),
            parent: asset.name.toUpperCase(), // Add parent reference
          })),
        }

        // Add any additional properties needed for styling
        if (
          ["sales", "expenses", "income/loss"].includes(
            asset.name.toLowerCase()
          )
        ) {
          baseData.isParent = true
        }
        if (
          asset.name.toLowerCase() === "income/loss" &&
          parseFloat(asset.total_amount) < 0
        ) {
          baseData.isNegative = true
        }

        return baseData
      })

      setTableData(formattedData)
    }

    setShowLoader(false)
  }

  useEffect(() => {
    fetchTableData()
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterConfig])

  const handleClick = (record, header) => {
    // if (record.parent === "EXPENSES" && header.label !== "Type") {
      const monthIndex = excelHeaders.findIndex((h) => h.label === header.label); // Find the month index

      if (monthIndex > 0) {
        const year = filterConfig.year; // Use the selected year from filterConfig
        const month = monthIndex; // Month is already 1-based (January = 1, February = 2, etc.)
        const startDate = dayjs(`${year}-${String(month).padStart(2, "0")}-01`).startOf("month").format("YYYY-MM-DD");
        const endDate = dayjs(`${year}-${String(month).padStart(2, "0")}-01`).endOf("month").format("YYYY-MM-DD");
        const formattedType = record.parent
              .toLowerCase()              // "project expenses"
              .replace(/\s+/g, '_')       // "project_expenses"


        const modalPayload = {
          expense_type_id: record.expense_type_id, // Use expense_type_id from the record
          start_date: startDate,
          end_date: endDate,
          type: formattedType
        };

        setModalData([modalPayload]); // Wrap modalPayload in an array
        setShowModal(true); // Show the modal
      }
    // }
  };

  return (
    <div>
      <div className="page">
        <Navbar
          onCollapse={(inactive) => {
            setInactive(inactive)
          }}
          active={"FINANCIAL REPORT"}
        />
      </div>

      <div className={`manager-container ${inactive ? "inactive" : "active"}`}>
        {/* headers */}
        <Row className="mb-1">
          <Col xs={6}>
            <h1 className="page-title"> Income Statement Report </h1>
          </Col>
          <Col xs={6} className="d-flex justify-content-end">
            <button className="add-btn text-center">{handleToCSV()}</button>
          </Col>
        </Row>

        <div className="tab-content">
          <div className="ms-3 PO-filters PI-filters d-flex mb-4">
            <DatePicker
              picker="year"
              defaultValue={dayjs().year(filterConfig.year)}
              allowClear={false} // Disable the close icon
              onChange={(date, dateString) => {
                if (date) {
                  setFilterConfig((prev) => ({
                    ...prev,
                    year: date.year(),
                  }))
                } else {
                  setFilterConfig((prev) => ({
                    ...prev,
                    year: null,
                  }))
                }
              }}
            />
          </div>

          {/* Table Content */}
          <div
            className="overflow-scroll ant-table-container"
            style={{
              margin: "20px",
            }}
          >
            <AntTable
              pagination={false}
              loading={showLoader}
              columns={excelHeaders.map((header) => {
                return {
                  title: header.label,
                  dataIndex: header.key,
                  key: header.key,
                  align: `${header.label === "Type" ? "start" : "end"}`,
                  render: (text, record) => {
                    const isIncomeLoss =
                      record.name === "INCOME/LOSS" && header.label !== "Type"
                    const isNegative = parseFloat(text) < 0
                    const color = isIncomeLoss
                      ? isNegative
                        ? "red"
                        : "green"
                      : "inherit"
                    const isNumber = !isNaN(text) && isFinite(text)
                    const formattedText = isNumber ? numberFormat(text) : text

                    return (
                      <div
                        style={{
                          color,
                          width: header.label === "Type" ? "350px" : "",
                          cursor:
                            ( record.parent === ("OPERATING EXPENSES") || 
                            record.parent === ("PROJECT EXPENSES") ) &&
                            // header.label !== "Type" &&
                            text !== "0.00" // Disable pointer if value is 0.0
                              ? "pointer"
                              : "inherit",
                        }}
                        onClick={
                         ( record.parent === ("OPERATING EXPENSES") || 
                          record.parent === ("PROJECT EXPENSES") ) &&
                          // header.label !== "Type" &&
                          text !== "0.00" // Disable click if value is 0.0
                            ? () => handleClick(record, header) // Pass record and header to handleClick
                            : undefined
                        }
                      >
                        {formattedText}
                      </div>
                    )
                  },
                }
              })}
              dataSource={tableData}
            />
          </div>
        </div>
      </div>
      {/* Add the modal */}
      <ExpensesBreakdownModal
        show={showModal}
        onHide={() => setShowModal(false)} // Close the modal
        data={modalData} // Pass data to the modal
      />
    </div>
  )
}
