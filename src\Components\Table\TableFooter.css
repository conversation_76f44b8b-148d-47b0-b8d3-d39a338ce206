.tableFooter {
  background-color: rgba(146, 146, 146, 0.22);
  min-width: 414px;
  width: 100%;
  padding: 8px 15px;
  font-weight: 500;
  text-align: left;
  font-family: var(--primary-font-medium);
  font-size: 16px;
  color: #2c3e50;
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 0px;
  overflow-x:auto !important;
  display: flex !important;
  flex-wrap: wrap;
}

.page-count-cont {
  margin-top: 1% !important;
}

.page-count-cont-end {
  margin-top: 1% !important;
  display: flex;
  justify-content: flex-end;
} 

.button {
  border: none;
  padding: 7px 14px;
  border-radius: 10px;
  cursor: pointer;
  margin-right: 4px;
  margin-left: 4px;
}

.activeButton {
  color: white;
  background: #5ac8e1;
}

.inactiveButton {
  color: #2c3e50;
  background: #f9f9f9;
}

.navigateButton {
  color: #2c3e50;
  background: #f9f9f9;
}

.rows-input {
  margin-right: 1%;
  margin-left: 1%;
}

.page-number-input {
  margin-right: 1%;
  margin-left: 1%;
  /* justify-content: end; */
}

.disable {
  background-color: rgb(151, 145, 145);
}

@media only screen and (max-width: 1000px){ 

  .register-mobile, .user-mobile {
    min-width: 492px;
  }

  .search-mobile {
    min-width: 795px;
  }

  .company-mobile {
    min-width: 462px;
  }
}