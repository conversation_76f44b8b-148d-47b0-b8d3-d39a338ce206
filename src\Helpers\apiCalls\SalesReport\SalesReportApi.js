import { getToken, getUser } from "../../Utils/Common"
import Moment from "moment"
import { getAPICall, postAPICall } from "../axiosMethodCalls"
import { itemsMockData } from "../../mockData/mockData"

export const createJournalEntry = async (info) => {
  try {
    const response = await postAPICall(
      process.env.REACT_APP_LINK + "journal_entries/create",
      {
        requester: getUser(),
        token: getToken(),
        date: info.date,
        ref_no: info.ref_no,
        remarks: info.remarks,
        total_debit: info.total_debit,
        total_credit: info.total_credit,
        project_ids: info.project_ids,
        expense_type_ids: info.expense_type_ids,
        debits: info.debits,
        credits: info.credits,
        item_remarks: info.item_remarks,
      }
    );
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};

export const editJournalEntry = async (info) => {
  try {
    const response = await postAPICall(
      process.env.REACT_APP_LINK + "journal_entries/update",
      {
        requester: getUser(),
        token: getToken(),
        journal_entry_id: info.journal_entry_id, // Match backend parameter name
        date: info.date,
        remarks: info.remarks,
        ref_no: info.ref_no,
        total_debit: info.total_debit,
        total_credit: info.total_credit,
        project_ids: info.project_ids,
        expense_type_ids: info.expense_type_ids,
        debits: info.debits,
        credits: info.credits,
        item_remarks: info.item_remarks,
      }
    );
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
}

export const searchReceivablesAgingReport = async (data) => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "reports/get_receivables_aging",
      {
        requester: getUser(),
        token: getToken(),
        customer_id: data.customer_id,
        project_id: data.project_id,
        date_from: data.date_from
          ? Moment(data.date_from).format("YYYY-MM-DD")
          : "",
        date_to: data.date_to ? Moment(data.date_to).format("YYYY-MM-DD") : "",
        payable: data.payable,
        paid: data.paid,
      }
    )
    return { data: response.data }
  } catch (error) {
    return { error: error }
  }
}

export const getFinancialReport = async (payload) => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "reports/get_financial_report",
      {
        requester: getUser(),
        token: getToken(),
        ...payload,
      }
    )
    return { data: response.data }
  } catch (error) {
    return { error: error }
  }
}

export const getPaymentByExpense = async (payload) => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "reports/get_payment_by_expense",
      {
        requester: getUser(),
        token: getToken(),
        ...payload,
      }
    )
    return { data: response.data }
  } catch (error) {
    return { error: error }
  }
}

export const getJournalEntryReport = async (payload) => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "journal_entries/get_all_journal_entry",
      {
        requester: getUser(),
        token: getToken(),
        ...payload,
      }
    )
    return { data: response.data }
  } catch (error) {
    return { error: error }
  }
}

export const getJournalEntry = async (id) => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "journal_entries/get_journal_entry",
      {
        requester: getUser(),
        token: getToken(),
        journal_entry_id: id,
      }
    );
    return { data: response.data };
  } catch (error) {
    return { error: error };
  }
};

export const getProjectSalesReport = async (payload) => {
  try {
    const response = await getAPICall(
      // process.env.REACT_APP_LINK + "reports/get_project_sales",
      process.env.REACT_APP_LINK + "project_invoice_payments/search",
      {
        requester: getUser(),
        token: getToken(),
        ...payload,
      }
    )
    return { data: response.data }
  } catch (error) {
    return { error: error }
  }
};

export const deleteJournalEntry = async (id) => {
  try {
    const response = await postAPICall(
      process.env.REACT_APP_LINK + "journal_entries/delete",
      {
        requester: getUser(),
        token: getToken(),
        journal_entry_id: id,
      }
    );
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};

export const postJournalEntry = async (id, is_posted = 1) => {
  try {
    const response = await postAPICall(
      process.env.REACT_APP_LINK + "journal_entries/post_journal_entry",
      {
        requester: getUser(),
        token: getToken(),
        journal_entry_id: id,
        is_posted: is_posted
      }
    );
    return { data: response.data };
  } catch (error) {
    return { error: error.response };
  }
};

export const getProjects = async () => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "projects/search",
      {
        requester: getUser(),
        token: getToken(),
      }
    );
    return { data: response.data };
  } catch (error) {
    return { error: error };
  }
};

export const getBankReconciliation = async (payload) => {
  try {
    const response = await getAPICall(
      process.env.REACT_APP_LINK + "reports/get_bank_reconciliation",
      {
        requester: getUser(),
        token: getToken(),
        ...payload,
      }
    )
    return { data: response.data }
  } catch (error) {
    return { error: error }
  }
}
