import React, { useState } from "react";
import { <PERSON><PERSON>, Col, Form, Row, Table } from "react-bootstrap";
import { Select, DatePicker } from "antd";
import { useNavigate, useParams } from "react-router-dom";
import toast from "react-hot-toast";
import "react-autocomplete-input/dist/bundle.css";
import "react-bootstrap-typeahead/css/Typeahead.css";

import {
  getTodayDateISO,
  numberFormat,
  sanitizeNumbers,
  toastStyle,
} from "../../Helpers/Utils/Common";
import Navbar from "../../Components/Navbar/Navbar";
import "../Purchases/PurchaseOrders/PurchaseOrders.css";
import "./Project.css";
import { Fragment } from "react";
import { useEffect } from "react";

import {
  createProject,
  updateProject,
  getProject,
  getProjectTypes,
} from "../../Helpers/apiCalls/Manage/Projects";
import { getAllCustomer } from "../../Helpers/apiCalls/Manage/CustomerAPI";
import InputError from "../../Components/InputError/InputError";
import { validateProject } from "./../../Helpers/Validation/Project/ProjectValidation";
import trash from "../../Assets/Images/trash.png";
import { getDistributor } from "../../Helpers/apiCalls/Distributor/DistributorApi";
import dayjs from "dayjs";
import { set } from "date-fns";

/**
 *  -- COMPONENT: FORM TO ADD OR EDIT FRANCHISEE SALES INVOICE
 */
function FormProject({ add, edit, renew }) {
  const navigate = useNavigate();

  const [inactive, setInactive] = useState(true);
  const [isSubmitClicked, setIsSubmitClicked] = useState(false);
  /**
   *  @po_id - param for add purchase invoice form
   *  @id - param for edit purchase invoice form
   */
  const { id } = useParams();
  const today = getTodayDateISO();

  // FRANCHISEE INVOICE DETAILS HANDLER
  const [projectDetails, setProjectDetails] = useState({
    name: "",
    project_date: "",
    start_date: "",
    renewal_date: "",
    contact_number: "",
    customer_id: "",
    contact_person: "",
    address: "",
    project_type: [],
    project_price: "0",
    vat_type: "vat_ex",
    subtotal: 0,
    vat_twelve: 0,
    vat_net: 0,
    withholding_tax: 0,
    grand_total: 0,
    one_time_fees_total: 0,
    recurring_cost_total: 0,
    distributor_id: "0",
    recurring_fee_temp: 0,
    wht_percent: "",
  });

  // const [selectedYear, setSelectedYear] = useState();

  const [recurring, setRecurring] = useState([
    {
      description: "",
      type: "daily",
      suffix: "day/s",
      periods: "",
      prices: "",
      totals: "",
      amounts: "",
    },

  ]);

  const [oneTimeFees, setOneTimeFees] = useState([
    {
      description: "",
      type: "",
      suffix: "",
      period: "",
      amount: "",
    },
  ]);

  //ERROR HANDLING
  const [isError, setIsError] = useState({
    name: false,
    project_date: false,
    //start_date: false,
    contact_number: false,
    customer_id: false,
    contact_person: false,
    address: false,
    project_type: false,
    prices: false,
    received_items_table: false,
  });

  // DATA HANDLERS
  const [customersData, setCustomersData] = useState([]);
  const [customerInfo, setCustomerInfo] = useState([]);
  const [distributors, setDistributors] = useState([]);
  const [projectTypes, setProjectTypes] = useState([]);
  const [isWHTEnabled, setIsWHTEnabled] = useState(false); // For checkbox
  const [selectedWHT, setSelectedWHT] = useState(""); // For dropdown selection

  async function fetchDistributors() {
    const response = await getDistributor("");
    if (response.data) {
      const res = response.data.data.map((row) => {
        var info = row;
        info.label = row.name;
        info.value = row.id;
        return info;
      });
      setDistributors(res);
    }
  }

  async function fetchCustomer() {
    setCustomersData([]);
    const response = await getAllCustomer();

    if (
      response &&
      response.data &&
      response.data.data
    ) {
      let result = response.data.data.map((a) => {
        return {
          label: a.name,
          value: a.id,
          contact_number: a.contact_number,
          contact_person: a.contact_person,
          address: a.address,
          company: a.company,
          email: a.email,
        };
      });
      setCustomersData(result);
      setCustomerInfo(result);
    }
  }
  
  async function fetchProject() {
    const response = await getProject(id);
    if (response.data) {
        var data = response.data.data[0];
        const tempRecurring = data.recurring_cost?.map((row) => {
            const type = row.type || "daily"; // Default to "daily"
            
            let tempSuffix = "day/s"; // Default suffix for daily
            if (type === "weekly") tempSuffix = "week/s";
            else if (type === "monthly") tempSuffix = "month/s";
            else if (type === "yearly") tempSuffix = "year/s";

            return {
                ...row,
                price: sanitizeNumbers(row.price),
                recurring_fee_temp: sanitizeNumbers(row.total),
                type,  // Ensures "daily" is set if missing
                suffix: tempSuffix, // Ensures correct suffix
                amounts: row.amount,
            };
        });
      setSelectedWHT(data.wht_percent)

      setRecurring(tempRecurring);

      var tempTotalOneTime = 0;
      const tempOneTimeFees = data.one_time_fee?.map((row) => {
        var tempSuffix = "";
        if (row.type === "daily") {
          tempSuffix = "day/s";
        } else if (row.type === "weekly") {
          tempSuffix = "week/s";
        } else if (row.type === "monthly") {
          tempSuffix = "month/s";
        } else if (row.type === "yearly") {
          tempSuffix = "year/s";
        } else if (row.type === "one_time") {
          tempSuffix = "one time";
        }
        tempTotalOneTime += parseFloat(row.amount);
        return {
          ...row,
          suffix: tempSuffix, // Add a suffix for one-time fees
        };
      });
      setOneTimeFees(tempOneTimeFees);
      setProjectDetails({
        ...data,
        subtotal:
          parseFloat(tempTotalOneTime) + parseFloat(data.recurring_cost_total),
        one_time_fees_total: tempTotalOneTime,
        recurring_cost_total: parseFloat(data.recurring_cost_total),
        grand_total: parseFloat(data.grand_total),
        project_type: data.project_types?.map(
          (item) => item.project_type_name_id
        ),
      });
    } else {
      toast.error(response.error.data.messages.error, {
        style: toastStyle(),
      });
    }
  }

  async function fetchAllProjectTypes() {
    setProjectTypes([]);
    const response = await getProjectTypes();
    if (response.data) {
      const res = response.data.data.map((row) => {
        return {
          value: row.id,
          label: row.name,
        };
      });
      setProjectTypes(res);
    }
  }

  //ADD FUNCTIONS
  async function handleCreateProject() {
    if (isSubmitClicked) {
      return;
    }

    if (validateProject(projectDetails, recurring, oneTimeFees, setIsError)) {
      setIsSubmitClicked(true);
      const response = await createProject(
        {...projectDetails, wht_percent: selectedWHT},
        recurring,
        oneTimeFees
      );
      if (response.data) {
        if (response.data?.message === "Data already exists") {
          setIsSubmitClicked(false);
          toast.error("Data already exists", { style: toastStyle() });
        } else {
          setIsSubmitClicked(false);
          toast.success("Successfully created Project", {
            style: toastStyle(),
          });
          setTimeout(
            () => navigate("/project/print/" + response.data.project_id),
            1000
          );
        }
      } else {
        setIsSubmitClicked(false);
        toast.error(response.error.data.messages.error, {
          style: toastStyle(),
        });
      }
    } else {
      toast.error("Please fill in all fields", {
        style: toastStyle(),
      });
    }
  }

  const handleWHTChange = (value) => {
    setSelectedWHT(value);
  
    if (value) {
      const percentage = parseFloat(value) / 100;
      const net = parseFloat(projectDetails.subtotal) / 1.12; // VAT net calculation
      const withholdingTax = net * percentage; // Withholding tax calculation
      const vatTwelve = parseFloat(projectDetails.subtotal) - net; // VAT amount
      const grandTotal = parseFloat(projectDetails.subtotal) - withholdingTax;
  
      setProjectDetails((prevDetails) => ({
        ...prevDetails,
        withholding_tax: withholdingTax.toFixed(2),
        vat_net: net.toFixed(2),
        vat_twelve: vatTwelve.toFixed(2),
        grand_total: grandTotal.toFixed(2),
        is_wht: 1,
      }));
    } else {
      // If no WHT is selected, reset WHT and recalculate grand total
      setProjectDetails((prevDetails) => ({
        ...prevDetails,
        withholding_tax: 0,
        grand_total: prevDetails.subtotal,
        is_wht: 0,
      }));
    }
  };

  //Recurring Cost
  function handleRecurringChange(e, index) {
    const { name, value } = e.target;
    var temp = [...recurring];

    if (name === "type") {
      temp[index][name] = value;
      if (value === "daily") {
        temp[index].suffix = "day/s";
      } else if (value === "weekly") {
        temp[index].suffix = "week/s";
      } else if (value === "monthly") {
        temp[index].suffix = "month/s";
      } else if (value === "yearly") {
        temp[index].suffix = "year/s";
      }
    } else {
      temp[index][name] = value;
    }

    const periods = parseInt(temp[index].periods) || 0;
    const price = parseFloat(temp[index].prices) || 0;

    temp[index].recurring_fee_temp = periods * price;

    const updatedRecurringFeeTotal = temp.reduce((sum, item) => sum + (parseFloat(item.recurring_fee_temp) || 0), 0);

    calculateTotal(temp);
    setRecurring(temp);

    setProjectDetails((prev) => ({
      ...prev,
      recurring_cost_total: updatedRecurringFeeTotal,
    }));

  }

  // //Recurring Cost
  // function handleOneTimeChange(e, index) {
  //   const { name, value } = e.target;
  //   var temp = [...oneTimeFees];

  //   if (name === "type") {
  //     temp[index][name] = value;
  //     if (value === "daily") {
  //       temp[index].suffix = "day/s";
  //     } else if (value === "weekly") {
  //       temp[index].suffix = "week/s";
  //     } else if (value === "monthly") {
  //       temp[index].suffix = "month/s";
  //     } else if (value === "yearly") {
  //       temp[index].suffix = "year/s";
  //     }
  //   } else {
  //     temp[index][name] = value;
  //   }

  //   calculateOneTimeFeeTotal(temp);
  //   setOneTimeFees(temp);
  // }

  //Cost
  function handleCostChange(e, index) {
    const { name, value } = e.target;
    const newList = [...oneTimeFees];
  
    if (name === "type") {
      newList[index][name] = value;
      if (value === "daily") {
        newList[index].suffix = "day/s";
      } else if (value === "weekly") {
        newList[index].suffix = "week/s";
      } else if (value === "monthly") {
        newList[index].suffix = "month/s";
      } else if (value === "yearly") {
        newList[index].suffix = "year/s";
      } else if (value === "one_time") {
        newList[index].suffix = "one time";
      }
    } else {
      newList[index][name] = value; 
    }

    const amount = parseFloat(newList[index].amount) || 0;

    newList[index].one_time_fees_total = amount;

    const UpdatedOneTimeFeeTotal = newList.reduce((sum, item) => sum + (parseFloat(item.one_time_fees_total) || 0), 0);

    calculateOneTimeFeeTotal(newList);
    setOneTimeFees(newList);

    setProjectDetails((prev) => ({
      ...prev,
      one_time_fees_total: UpdatedOneTimeFeeTotal,
      grand_total: UpdatedOneTimeFeeTotal + parseFloat(prev.recurring_cost_total || 0),
    }));

  }
  

  const handleKeyPress = (e) => {
    const allowedChars = /[0-9.]/;
    if (!allowedChars.test(e.key)) {
      e.preventDefault();
    }
  };

  function handleDelRow(id) {
    setRecurring(prevRecurring => {
      const newList = prevRecurring.filter(item => item.id !== id);
      calculateTotal(newList);
      return newList;
    });
  }

  function handleDelRowCost(id) {
    setOneTimeFees(prevFees => {
      const newList = prevFees.filter(item => item.id !== id);
      calculateOneTimeFeeTotal(newList);
      return newList;
    });
  }

  function handleAddNewRow() {
    const newItem = {
      description: "",
      type: "daily",
      suffix: "day/s",
      periods: "1",
      prices: "",
      year: "",
      totals: "",
    };
    setRecurring((oldItems) => [...oldItems, newItem]);
  }

  function handleAddNewRowCost() {
    const newItem = {
      description: "",
      type: "yearly",
      suffix: "year",
      period: "1",
      account_type: "",
      amount: "",
    };
    setOneTimeFees((oldItems) => [...oldItems, newItem]);
  }
  //EDIT FUNCTIONS
  async function handleUpdatePI() {
  
    if (validateProject(projectDetails, recurring, oneTimeFees, setIsError)) {
      const response = await updateProject(
        {...projectDetails,
        wht_percent: selectedWHT},
        recurring,
        oneTimeFees,
        );
    
      if (response?.data) {
        toast.success("Successfully updated Project", {
          style: toastStyle(),
        });
        setTimeout(() => navigate(-1), 1000);
      } else {
        toast.error(response?.error?.data?.messages?.error || "Update failed", {
          style: toastStyle(),
        });
      }
    } else {
      toast.error("Please fill in all fields", {
        style: toastStyle(),
      });
    }
  }
  

  function handleAddRecurringFee(newFee) {
    setRecurring((prevRecurring) => {
        const updatedRecurring = [...prevRecurring, newFee];
        calculateTotal(updatedRecurring);
        return updatedRecurring;
    });
  }

  //HANDLES
  const handleSubmit = () => {
    if (add) handleCreateProject();
    else if (edit) handleUpdatePI();
    else if (renew) handleUpdatePI();
  };

  const handleChange = (e, field) => {
    if (field === "customer_id") {
      customerInfo
        .filter((info) => info.value === e)
        .map((data) => {
          setProjectDetails((prevState) => ({
            ...prevState,
            customer_id: e,
            email: data.email,
            contact_number: data.contact_number,
            contact_person: data.contact_person,
            address: data.address,
            company: data.company,
          }));
        });
    } else {
      const { name, value } = e.target;
      setProjectDetails((prevState) => ({
        ...prevState,
        [name]: value,
      }));
    }
  };

  const handleVatChange = (e, isSelect) => {
    const { name, value } = e.target;
    if (name === "customer_id") {
      customerInfo
        .filter((info) => info.value === value)
        .map((data) => {
          setProjectDetails((prevState) => ({
            ...prevState,
            customer_id: value,
            email: data.email,
            contact_number: data.contact_number,
            contact_person: data.contact_person,
            address: data.address,
            company: data.company,
          }));
        });
    } else {
      if (value === "vat_in") {
        const grossTotal =
          parseFloat(projectDetails.recurring_cost_total) +
          parseFloat(projectDetails.one_time_fees_total);
        var tempVatNet = parseFloat(grossTotal / 1.12);
        var tempWth = parseFloat(tempVatNet * 0.02);
        var temp12 = parseFloat(grossTotal - tempVatNet);

        setProjectDetails((prev) => ({
          ...prev,
          [name]: value,
          subtotal: grossTotal,
          vat_net: tempVatNet,
          vat_twelve: temp12,
          withholding_tax: tempWth,
          grand_total: parseFloat(grossTotal - tempWth),
        }));
      } else if (value === "vat_ex") {
        setProjectDetails((prev) => ({
          ...prev,
          [name]: value,
          subtotal:
            parseFloat(prev.one_time_fees_total) +
            parseFloat(prev.recurring_cost_total),
          vat_net: 0,
          vat_twelve: 0,
          withholding_tax: 0,
          grand_total:
            parseFloat(prev.one_time_fees_total) +
            parseFloat(prev.recurring_cost_total),
        }));
      }
    }
  };

  //USE EFFECTS
  useEffect(() => {
    if (edit || renew) {
      fetchProject();
    }
    fetchCustomer();
    fetchDistributors();
    fetchAllProjectTypes();
    calculateTotal(recurring);
  }, []);

  function calculateTotal(recurringData) {
    var temp_yearly = 0;
    var temp_monthly = 0;
    var temp_weekly = 0;
    var temp_daily = 0;

    let total = recurringData.reduce((sum, info) => sum + (Number(info.prices) * Number(info.periods)), 0);

    recurringData
      .filter((data) => data.suffix?.includes("year"))
      .forEach((info) => {
        temp_yearly += Number(info.prices) * Number(info.periods);
      });
    recurringData
      .filter((data) => data.suffix?.includes("month"))
      .forEach((info) => {
        temp_monthly += Number(info.prices) * Number(info.periods);
      });
    recurringData
      .filter((data) => data.suffix?.includes("week"))
      .forEach((info) => {
        temp_weekly += Number(info.prices) * Number(info.periods);
      });
    recurringData
      .filter((data) => data.suffix?.includes("day"))
      .forEach((info) => {
        temp_daily += Number(info.prices) * Number(info.periods);
      });

    const tempTotal =
      parseFloat(temp_yearly) +
      parseFloat(temp_monthly) +
      parseFloat(temp_weekly) +
      parseFloat(temp_daily);

    setProjectDetails((prev) => {
      const updatedRecurringCostTotal = total;
      const updatedSubtotal = parseFloat(prev.one_time_fees_total || 0) + updatedRecurringCostTotal;
      
      if (prev.vat_type === "vat_in") {
        const tempVatNet = parseFloat(updatedSubtotal / 1.12);
        const tempVatTwelve = parseFloat(updatedSubtotal - tempVatNet);
        
        // Calculate WHT if a percentage is selected
        let tempWth = 0;
        if (selectedWHT) {
          const whtPercentage = parseFloat(selectedWHT) / 100;
          tempWth = tempVatNet * whtPercentage;
        }

        return {
          ...prev,
          subtotal: updatedSubtotal,
          vat_net: tempVatNet,
          vat_twelve: tempVatTwelve,
          recurring_cost_total: updatedRecurringCostTotal,
          withholding_tax: tempWth.toFixed(2),
          grand_total: (updatedSubtotal - tempWth).toFixed(2),
          is_wht: selectedWHT ? 1 : 0,
        };
      } else {
        return {
          ...prev,
          subtotal: updatedSubtotal,
          vat_net: 0,
          vat_twelve: 0,
          recurring_cost_total: updatedRecurringCostTotal,
          withholding_tax: 0,
          grand_total: updatedSubtotal,
          is_wht: 0,
        };
      }
    });
  }

  function calculateOneTimeFeeTotal(newList) {
    var temp_yearly = 0;
    var temp_monthly = 0;
    var temp_weekly = 0;
    var temp_daily = 0;
    var temp_one_time = 0;

    newList
      .filter((data) => data.suffix?.includes("year"))
      .forEach((info) => {
        temp_yearly += Number(info.amount) * Number(info.period);
      });
    newList
      .filter((data) => data.suffix?.includes("month"))
      .forEach((info) => {
        temp_monthly += Number(info.amount) * Number(info.period);
      });
    newList
      .filter((data) => data.suffix?.includes("week"))
      .forEach((info) => {
        temp_weekly += Number(info.amount) * Number(info.period);
      });
    newList
      .filter((data) => data.suffix?.includes("day"))
      .forEach((info) => {
        temp_daily += Number(info.amount) * Number(info.period);
      });
    newList
      .filter((data) => data.suffix?.includes("one time"))
      .forEach((info) => {
        temp_one_time += Number(info.amount) * Number(info.period);
      });

    const tempTotal =
      parseFloat(temp_yearly) +
      parseFloat(temp_monthly) +
      parseFloat(temp_weekly) +
      parseFloat(temp_daily) +
      parseFloat(temp_one_time);
      
    setProjectDetails((prev) => {
      const updatedSubtotal = tempTotal + parseFloat(prev.recurring_cost_total);
      
      if (prev.vat_type === "vat_in") {
        const tempVatNet = parseFloat(updatedSubtotal / 1.12);
        const tempVatTwelve = parseFloat(updatedSubtotal - tempVatNet);
        
        // Calculate WHT if a percentage is selected
        let tempWth = 0;
        if (selectedWHT) {
          const whtPercentage = parseFloat(selectedWHT) / 100;
          tempWth = tempVatNet * whtPercentage;
        }

        return {
          ...prev,
          subtotal: updatedSubtotal,
          vat_net: tempVatNet,
          vat_twelve: tempVatTwelve,
          one_time_fees_total: tempTotal,
          withholding_tax: tempWth.toFixed(2),
          grand_total: (updatedSubtotal - tempWth).toFixed(2),
          is_wht: selectedWHT ? 1 : 0,
        };
      } else {
        return {
          ...prev,
          subtotal: updatedSubtotal,
          vat_net: 0,
          vat_twelve: 0,
          one_time_fees_total: tempTotal,
          withholding_tax: 0,
          grand_total: updatedSubtotal,
          is_wht: 0,
        };
      }
    });
  }

  return (
    <div>
      <div className="page">
        <Navbar
          onCollapse={(inactive) => {
            setInactive(inactive);
          }}
          active={"SALES"}
        />
      </div>

      <div className={`manager-container ${inactive ? "inactive" : "active"}`}>
        {/* header */}
        <div className="d-flex justify-content-between align-items-center pb-4">
          <h1 className="page-title mb-0">{add ? "ADD " : "EDIT "}PROJECT</h1>
        </div>

        {/* content */}
        <div className="edit-form">
          {/* FRANCHISEE SALES INVOICE DETAILS */}
          <Fragment>
            <Row className="pt-3 mb-2">
              <Col xs={5}>
                <span className="edit-label">
                  Project Name
                  <label className="badge-required">{` *`}</label>
                </span>
              </Col>
              <Col xs={3}>
                <span className="edit-label">
                  Contract Date
                  <label className="badge-required">{` *`}</label>
                </span>
              </Col>
              <Col>
                <span className="edit-label">
                  Start Date (deployment)
                  {/* <label className="badge-required">{` *`}</label> */}
                </span>
              </Col>
            </Row>
            <Row>
              <Col xs={5}>
                <Form.Control
                  type="text"
                  name="name"
                  value={projectDetails.name}
                  className="nc-modal-custom-input"
                  onChange={(e) => handleChange(e, "name")}
                  required
                  disabled={renew}
                />
                <InputError
                  isValid={isError.name}
                  message={"Project is required"}
                />
              </Col>
              <Col xs={3}>
                <DatePicker
                  className="nc-modal-custom-text-new-datepicker"
                  name="project_date"
                  value={
                    projectDetails.project_date
                      ? dayjs(projectDetails.project_date)
                      : null
                  }
                  onChange={(date, dateString) => {
                    setProjectDetails((prev) => ({
                      ...prev,
                      project_date: dateString,
                    }));
                  }}
                  disabled={renew}
                />
                <InputError
                  isValid={isError.project_date}
                  message={"Contract date is required"}
                />
              </Col>
              <Col>
                <DatePicker
                  className="nc-modal-custom-text-new-datepicker"
                  name="start_date"
                  value={
                    projectDetails.start_date
                      ? dayjs(projectDetails.start_date)
                      : null
                  }
                  onChange={(date, dateString) => {
                    setProjectDetails((prev) => ({
                      ...prev,
                      start_date: dateString,
                    }));
                  }}
                  disabled={renew}
                />
                {/*<InputError
                  isValid={isError.start_date}
                  message={"Start date is required"}
                />*/}
              </Col>
            </Row>
            <Row className="mt-4 mb-2">
              <Col>
                <span className="edit-label">
                  Customer Name
                  <label className="badge-required">{` *`}</label>
                </span>
              </Col>
              <Col>
                <span className="edit-label">Address</span>
              </Col>
            </Row>
            <Row className="mt-4 mb-2">
              <Col>
                {/* <Form.Select
                  type="select"
                  className="react-select-container"
                  classNamePrefix="react-select"
                  placeholder="Select Customer..."
                  name="customer_id"
                  value={projectDetails.customer_id}
                  onChange={(e) => handleChange(e, false)}
                  
                >
                  <option value="">Select a Customer...</option>
                  {customersData.map((data) => {
                    return (
                      <option key={data.value} value={data.value}>
                        {data.label}
                      </option>
                    );
                  })}
                </Form.Select> */}
                <Select
                  className="w-100"
                  options={customersData}
                  dropdownStyle={{ fontFamily: "var(--primary-font-regular)" }}
                  value={projectDetails.customer_id}
                  // onChange={(e) => {
                  //   setProjectDetails((prevState) => ({
                  //     ...prevState,
                  //     customer_id: e,
                  //   }));
                  // }}
                  onChange={(e) => handleChange(e, "customer_id")}
                  disabled={renew}
                  showSearch
                  filterOption={(input, option) =>
                    option?.label?.toLowerCase().includes(input.toLowerCase())
                  }
                />
                <InputError
                  isValid={isError.customer_id}
                  message={"Customer is required"}
                />
              </Col>
              <Col>
                <Form.Control
                  type="text"
                  name="address"
                  className="nc-modal-custom-text"
                  value={projectDetails.address}
                  onChange={(e) => handleChange(e, "address")}
                  disabled={renew}
                />
                <InputError
                  isValid={isError.address}
                  message={"Address is required"}
                />
              </Col>
            </Row>
            <Row className="mt-4 mb-2">
              <Col>
                <span className="edit-label">
                  Company
                  {/* <label className="badge-required">{` *`}</label> */}
                </span>
              </Col>
              <Col>
                <span className="edit-label">Contact Person</span>
              </Col>
            </Row>
            <Row>
              <Col>
                <Form.Control
                  type="text"
                  name="company"
                  value={projectDetails.company}
                  className="nc-modal-custom-text"
                  onChange={(e) => handleChange(e, "company")}
                  disabled={renew}
                />
              </Col>
              <Col>
                <Form.Control
                  type="text"
                  name="contact_person"
                  value={projectDetails.contact_person}
                  className="nc-modal-custom-text"
                  onChange={(e) => handleChange(e, "contact_person")}
                  disabled={renew}
                />
                <InputError
                  isValid={isError.contact_person}
                  message={"Contact person is required"}
                />
              </Col>
            </Row>
            <Row className="mt-3">
              <Col xs={4}>
                <span className="edit-label">Select Distributor</span>
                <Select
                  className="w-100"
                  options={[
                    { value: "0", label: "No distributor"},
                    ...distributors,
                  ]}
                  dropdownStyle={{ fontFamily: "var(--primary-font-regular)" }}
                  value={projectDetails.distributor_id}
                  onChange={(e) => setProjectDetails((prevState) => ({
                    ...prevState,
                    distributor_id: e
                  }))}
                  disabled={renew}
                  showSearch
                  filterOption={(input, option) =>
                    option?.label?.toLowerCase().includes(input.toLowerCase())
                  }
                />
              </Col>
              <Col xs={4}>
                <span className="edit-label">Pricing Model {/*Payment Structure ni sauna*/}</span>
                <span className="color-red"> *</span>

                {/* <Form.Select
                  onChange={(e) =>
                    setProjectDetails((prevState) => ({
                      ...prevState,
                      payment_structure: e.target.value,
                    }))
                  }
                  className="w-90 ms-0"
                >
                  <option value="One-Time Fee">One-Time Fee</option>
                  <option value="Subsciption Based">Subsciption Based</option>
                </Form.Select> */}
                <Select
                  className="w-100"
                  options={[
                    { value: "One-Time Fee", label: "One-Time Fee" },
                    { value: "Subscription Based", label: "Subscription Based" },
                  ]}
                  dropdownStyle={{ fontFamily: "var(--primary-font-regular)" }}
                  value={projectDetails.payment_structure}
                  onChange={(e) => {
                    setProjectDetails((prevState) => ({
                      ...prevState,
                      payment_structure: e,
                    }));
                  }}
                  disabled={renew}
                />
              </Col>
              <Col xs={4}>
                <Row>
                <Row>
                  <Col>
                  <span className="edit-label">Project Type</span>
                  <span className="color-red"> *</span>
                  </Col>
                </Row>
                  <Select
                    className="w-100"
                    options={projectTypes}
                    mode="multiple"
                    placeholder="Select Project Type"
                    onChange={(e) => {
                      setProjectDetails((prevState) => ({
                        ...prevState,
                        project_type: e
                      }));
                      setIsError((prev) => ({
                        ...prev,
                        project_type: false
                      }));
                    }}
                    value={projectDetails.project_type}
                    disabled={renew}
                    dropdownStyle={{
                      fontFamily: "var(--primary-font-regular)"
                    }}
                    allowClear={false}
                    clearIcon={true}
                  />
                </Row>
                {/* <Form.Select
                  onChange={(e) =>
                    // setProjectDetails((prevState) => ({
                    //   ...prevState,
                    //   project_type: e.target.value,
                    // }))
                  }
                  className="w-90 ms-0"
                >
                  {
                    systemProjectTypes.map((row) => (
                      <option value={row.value}>{row.label}</option>
                    ))
                  }
                </Form.Select> */}
              </Col>
              {/* <Col>
                <Row className="mt-2">
                  <span className="edit-label">Select Billing Date</span>
                  <ReactDatePicker className="form-control" 
                  selected={billingDate} onChange={(value) => setBillingDate(value)}
                  />
                </Row>
              </Col> */}
            </Row>
          </Fragment>
          {renew && (
            <Row className="mt-3">
              <Row>
                <span className="edit-label">Renewal Date</span>
              </Row>
              <Col xs={4}>
                <Form.Control
                  type="date"
                  name="renewal_date"
                  className="nc-modal-custom-text"
                  value={projectDetails.renewal_date}
                  defaultValue={today}
                  onChange={(e) => handleChange(e, "renewal_date")}
                />
                <InputError
                  isValid={isError.renewal_date}
                  message={"Renewal date is required"}
                />
              </Col>
            </Row>
          )}
          <Row className="mt-4 pt-3">
            <span className="edit-label mb-2">ONE TIME FEE</span>
            <div className="edit-purchased-items">
              {oneTimeFees.length !== 0 ? (
                <>
                  <Table>
                    <thead>
                      <tr>
                        <th className="color-gray">Descriptions</th>
                        {/* <th className="color-gray">Type</th> */}
                        {/* <th className="color-gray">Type</th> */}
                        {/* <th className="color-gray">Periods</th> */}
                        <th className="color-gray">Amount</th>
                      </tr>
                    </thead>
                    <tbody>
                      {oneTimeFees.map((item, index) => {
                        return (
                          <tr key={item.id}>
                            <td>
                              <Form.Control
                                type="text"
                                name="description"
                                defaultValue={item.description}
                                // value={item.descriptions}
                                onChange={(e) => handleCostChange(e, index)}
                              />
                              <InputError
                                isValid={isError.description}
                                message={"Cost is required"}
                              />
                            </td>
                            {/* <td>
                              <Form.Select
                                name="type"
                                value={item.type}
                                onChange={(e) => {
                                  handleCostChange(e, index);
                                }}
                              >
                                <option value="daily">Daily</option>
                                <option value="weekly">Weekly</option>
                                <option value="monthly">Monthly</option>
                                <option value="yearly">Yearly</option>
                                <option value="one_time">One Time</option>
                              </Form.Select>
                            </td> */}
                            {/* <td>
                              <Row>
                                <Col>
                                  <Form.Control
                                    type="number"
                                    name="period"
                                    value={item.period}
                                    onChange={(e) => {
                                      if (!renew) {
                                        handleCostChange(e, index);
                                      }
                                    }}
                                  />
                                </Col>
                                <Col>
                                  <Form.Control
                                    type="text"
                                    name="suffix"
                                    disabled
                                    value={item.suffix ?? ""}
                                  />
                                </Col>
                              </Row>
                            </td> */}
                            <td>
                              <Form.Control
                                type="text"
                                name="amount"
                                min={0}
                                defaultValue={item.amount}
                                // value={item.amount}
                                onChange={(e) => handleCostChange(e, index)}
                                onKeyPress={(e) => handleKeyPress(e)}
                              />
                              <InputError
                                isValid={isError.amount}
                                message={"Cost is required"}
                              />
                            </td>
                            <td className="text-center">
                              <img
                                src={trash}
                                onClick={() => handleDelRowCost(item.id)}
                                className="cursor-pointer"
                                alt=""
                              />
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </Table>
                  <InputError
                    isValid={isError.item_info}
                    message={"Please make sure all fields are filled"}
                  />
                </>
              ) : (
                <div className="entries-not-found">
                  There is no data recorded yet.
                </div>
              )}
            </div>
          </Row>
          {/* ADD ITEM BUTTON */}
          <Row className="pt-3 PO-add-item">
            <Button type="button" onClick={() => handleAddNewRowCost()}>
              Add One Time Fee
            </Button>
          </Row>
          <Row className="mt-4 pt-3">
            <span className="edit-label mb-2">Recurring Fee</span>
            <div className="edit-purchased-items">
              {recurring.length !== 0 ? (
                <>
                  <Table>
                    <thead>
                      <tr>
                        <th className="color-gray">Descriptions</th>
                        <th className="color-gray">Type</th>
                        <th className="color-gray">Periods</th>
                        <th className="color-gray">Amount</th>
                        {/* <th className="color-gray">Year</th> */}
                        <th className="color-gray">Total</th>
                        <th className="color-gray">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {recurring.map((item, index) => {
                        return (
                          <tr key={item.id}>
                            <td>
                              <Form.Control
                                type="text"
                                name="description"
                                value={item.description}
                                onChange={(e) =>
                                  handleRecurringChange(e, index)
                                }
                              />
                            </td>
                            <td>
                              <Form.Select
                                name="type"
                                value={item.type}
                                onChange={(e) => {
                                  handleRecurringChange(e, index);
                                }}
                              >
                                <option value="daily">Daily</option>
                                <option value="weekly">Weekly</option>
                                <option value="monthly">Monthly</option>
                                <option value="yearly">Yearly</option>
                              </Form.Select>
                            </td>
                            <td>
                              <Row>
                                <Col>
                                  <Form.Control
                                    type="number"
                                    name="periods"
                                    min = "0"
                                    value={item.periods}
                                    onChange={(e) => {
                                      if (!renew) {
                                        handleRecurringChange(e, index);
                                      }
                                    }}
                                  />
                                </Col>
                                <Col>
                                  <Form.Control
                                    type="text"
                                    disabled
                                    value={item.suffix ?? ""}
                                  />
                                </Col>
                              </Row>
                            </td>
                            <td>
                              <Form.Control
                                type="text"
                                name="prices"
                                defaultValue={item.prices}
                                // value={item.price}
                                onChange={(e) =>
                                  handleRecurringChange(e, index)
                                }
                                onKeyPress={(e) => handleKeyPress(e)}
                              />
                              <InputError
                                isValid={isError.prices}
                                message={"Cost is required"}
                              />
                            </td>
                            <td>
                              <Col xs={3} className="text-end">
                                <span className="edit-label align-middle vat-total-text text-end">
                                  {numberFormat(recurring[index]?.recurring_fee_temp || 0)}
                                </span>
                              </Col>
                            </td>
                            <td className="text-center">
                              <img
                                src={trash}
                                onClick={() => handleDelRow(item.id)}
                                className="cursor-pointer"
                                alt=""
                              />
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </Table>
                  <InputError
                    isValid={isError.item_info}
                    message={"Please make sure all fields are filled"}
                  />
                </>
              ) : (
                <div className="entries-not-found">
                  There is no Recurring Fee recorded yet.
                </div>
              )}
              <InputError
                isValid={isError.items}
                message={"Please add at least 1 Recurring"}
              />
            </div>
          </Row>

          {/* ADD ITEM BUTTON */}
          <Row className="pt-3 PO-add-item">
            <Button type="button" onClick={() => handleAddNewRow()}>
              Add Recurring Fee
            </Button>
          </Row>
          <Row className="align-right pt-3">
            <Col xs={2} className="text-end">
              <span className="edit-label color-gray">One Time Fees</span>
            </Col>
            <Col xs={1} className="text-end">
              <span className="edit-label align-middle">PHP</span>
            </Col>
            <Col xs={3} className="text-end">
              <Form.Control
                // type="number"
                name="one_time_fees_total"
                min={0}
                step="0.01"
                value={numberFormat(projectDetails.one_time_fees_total) || 0}
                className="align-middle nc-modal-custom-text text-end"
                // onChange={(e) => handleChange(e)}
                disabled
              />
            </Col>
          </Row>
          <Row className="align-right pt-3">
            <Col xs={2} className="text-end">
              <span className="edit-label color-gray">Recurring Fees</span>
            </Col>
            <Col xs={1} className="text-end">
              <span className="edit-label align-middle">PHP</span>
            </Col>
            <Col xs={3} className="text-end">
              <Form.Control
                // type="number"
                name="recurring_cost_total"
                min={0}
                step="0.01"
                value={numberFormat(projectDetails.recurring_cost_total) || 0}
                className="align-middle nc-modal-custom-text text-end"
                disabled
                // onChange={(e) => handleChange(e)}
              />
            </Col>
          </Row>
          <Row className="align-right pt-3">
            <Col className="text-end">
              <Form.Check
                inline
                label="VAT Ex"
                name="vat_type"
                type="radio"
                value="vat_ex"
                checked={projectDetails.vat_type === "vat_ex"}
                onClick={(e) => {
                  handleVatChange(e);
                }}
                disabled={renew}
              />
              <Form.Check
                inline
                label="VAT In"
                name="vat_type"
                type="radio"
                value="vat_in"
                checked={projectDetails.vat_type === "vat_in"}
                onClick={(e) => {
                  handleVatChange(e);
                }}
                disabled={renew}
              />
            </Col>
          </Row>

          {projectDetails.vat_type === "vat_in" && (
            <>
              <Row className="align-right pt-3">
                <Col xs={2} className="text-end">
                  <span className="edit-label color-gray">Subtotal</span>
                </Col>
                <Col xs={1} className="text-end">
                  <span className="edit-label vat-total-text align-middle">
                    PHP
                  </span>
                </Col>
                <Col xs={3} className="text-end">
                  <span className="edit-label align-middle vat-total-text text-end">
                    {numberFormat(projectDetails.subtotal)}
                  </span>
                </Col>
              </Row>
              <Row className="align-right pt-3">
                <Col xs={2} className="text-end">
                  <span className="edit-label color-gray">12% VAT</span>
                </Col>
                <Col xs={1} className="text-end">
                  <span className="edit-label vat-total-text align-middle">
                    PHP
                  </span>
                </Col>
                <Col xs={3} className="text-end">
                  <span className="edit-label align-middle vat-total-text text-end">
                    {numberFormat(projectDetails.vat_twelve)}
                  </span>
                </Col>
              </Row>
              <Row className="align-right pt-3">
                <Col xs={2} className="text-end">
                  <span className="edit-label color-gray">Net of VAT</span>
                </Col>
                <Col xs={1} className="text-end">
                  <span className="edit-label vat-total-text align-middle">
                    PHP
                  </span>
                </Col>
                <Col xs={3} className="text-end">
                  <span className="edit-label align-middle vat-total-text text-end">
                    {numberFormat(projectDetails.vat_net)}
                  </span>
                </Col>
              </Row>
              <Row className="align-right pt-3">
                <Col xs={2} className="text-end">
                  <span className="edit-label color-gray">WHT</span>
                </Col>
                <Col xs={4} className="text-end d-flex justify-content-end align-items-center">
                  <Form.Select
                    value={selectedWHT}
                    onChange={(e) => handleWHTChange(e.target.value)}
                    className="edit-label color-gray w-50"
                    style={{ width: "100px" }}
                  >
                    <option value="">Select</option>
                    <option value="1">1%</option>
                    <option value="2">2%</option>
                    <option value="3">3%</option>
                    <option value="5">5%</option>
                    <option value="12">12%</option>
                  </Form.Select>
                </Col>
              </Row>
              <Row className="align-right pt-3">
                <Col xs={2} className="text-end">
                  <span className="edit-label vat-total-text align-middle">PHP</span>
                </Col>
                <Col xs={3} className="text-end">
                  <span className="edit-label align-middle vat-total-text text-end" style={{ minWidth: "100px" }}>
                    {projectDetails.withholding_tax
                      ? numberFormat(parseFloat(projectDetails.withholding_tax).toFixed(2))
                      : "0.00"}
                  </span>
                </Col>
              </Row>
            </>
          )}
          <Row className="align-right py-5">
            <Col xs={2} className="text-end">
              <span className="edit-label color-gray grand-total-text">
                Grand Total
              </span>
            </Col>
            <Col xs={1} className="text-end">
              <span className="edit-label align-middle grand-total-text">
                PHP
              </span>
            </Col>
            <Col xs={3} className="text-end">
              <span className="edit-label align-middle grand-total-text text-end">
                {numberFormat(projectDetails.grand_total)}
              </span>
            </Col>
          </Row>
          {/* FOOTER: CANCEL & SUBMIT BUTTONS */}
          <div className="d-flex justify-content-end pt-5 pb-3">
            <button
              type="button"
              className="button-secondary me-3"
              onClick={() => navigate(-1)}
            >
              Cancel
            </button>
            <button
              type="button"
              className="button-primary"
              onClick={handleSubmit}
            >
              Submit
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

FormProject.defaultProps = {
  add: false,
  edit: false,
};

export default FormProject;
