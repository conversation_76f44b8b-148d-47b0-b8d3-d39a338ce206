    .select-status {
        font-size: 9px;
    }

    .form-control {
        border-radius: 10px !important;
    }

    .page-title {
        font-family: var(--primary-font-medium);
        font-size: 35px;
        line-height: 42px;
        color: #5E5E5E;
        text-transform: uppercase;
        margin-top: 2%;
    }

    .btn-group, .dropdown-toggle, .add-btn {
        font-family: var(--primary-font-medium);
        font-weight: lighter;
        font-size: medium;
        height: 60%;
        width: 20%;
        border: none;
        padding: 1% 0%;
        margin-left: 3%;
        background: #5ac8e1 !important;
        border-color: transparent !important;
        border-radius: 10px;
        color: #ffff;
        align-items: baseline;
    }

    .dropdown-toggle {
        padding: 0 !important;
        margin: 0 !important;
    }

    .dropdown-toggle:focus {
        outline: none !important;
        box-shadow: none !important;
    }

    .btn-group, .dropdown-toggle, .add-btn:hover {
        background: #5ac8e1 !important;
    }

    .margin-top-3 {
        margin-top: 3%;
    }

    .search-bar {
        font-family: var(--primary-font-medium);
        font-weight: lighter;
        font-size: medium;
        border: none !important;
        background: #ffffff;
        box-shadow: 0px 0px 1px 1px rgb(0 0 0 / 25%);
        border-radius: 20px;
        width: 60%;
        padding: 1% 3% 1% 6%;
        margin-bottom: 3%;
        margin-left: 3%;
        background-image: url("../../Assets/Images/Navbar/search.png");
        background-repeat: no-repeat;
        background-position: 4% 50%;
    }

    .table {
        /* display: flex; */
        /* flex-direction: row; */
        justify-content: center;
        align-content: center;
        align-items: center;
        align-self: center;
        margin-bottom: 0% !important;
    }
    
    .tbody,
    td,
    tfoot,
    th,
    thead,
    tr {
        border-color: inherit;
        border-style: solid;
        border-width: 0;
        border: collapse !important;
        /* text-align: center !important; */
    }

    .text-green {
        color: #5ac8e1!important;
    }

    .nc-modal-custom-select {
        font-family: var(--primary-font-medium);
        font-weight: normal;
        font-size: medium;
        border-radius: 10px !important;
        border: #B9B9B9 1px solid !important;
        width: 100% !important;
        /* margin-bottom: 5% !important; */
        height: 5vh;
        padding: 1.5% !important;
    }

    .nc-modal-custom-select-table {
        font-family: var(--primary-font-medium);
        font-weight: normal;
        font-size: medium;
        border-radius: 10px !important;
        border: #B9B9B9 1px solid !important;
        width: 100% !important;
        /* margin-bottom: 5% !important; */
        height: 2vw !important;
    }

    .nc-modal-custom-row-box {
        background: #FFFFFF;
        border: 1px solid #B9B9B9 !important;
        border-radius: 10px !important;
        width: 100% !important;
        margin-left: 0% !important;
        margin-right: 0% !important;
        align-self: center !important;
        font-family: var(--primary-font-medium);
    }

    .m-divider {
        border-top: 2px solid #B9B9B9;
        /* height: 1vh; */
        width: auto !important;
    }

    .italized {
        font-family: var(--primary-font-medium);
        font-weight: 100 !important;
        font-size: small;
        font-style: italic;
        color: #5E5E5E;
    }

    .required {
        font-family: var(--primary-font-medium);
        font-weight: 400 !important;
        font-style: italic;
        font-size: small;
        color: #DC3545 !important;
    }

    .sub-span {
        font-family: var(--primary-font-medium);
        font-weight: 100 !important;
        font-size: small;
        font-style: normal;
        color: #5E5E5E;
    }

    .custom-modal-body-title-employee {
        margin-top: 10%;
        text-align: left !important;
        font-family: var(--primary-font-medium);
        font-style: normal;
        font-weight: 900;
        font-size: 20px;
        color: #545454 !important;
    }

    .custom-modal-body-title-supplier-details {
        margin-top: 10%;
        padding-left: 4% !important;
        text-align: left !important;
        font-family: var(--primary-font-medium);
        font-style: normal;
        font-weight: 900;
        font-size: xx-large;
        color: #545454 !important;
        background-image: url("../../Assets/Images/Modal/supplierdeets.png");
        background-repeat: no-repeat;
        background-position: 0% 50%;
    }

    .custom-modal-body-title-bank-details {
        margin-top: 10%;
        padding-left: 4% !important;
        text-align: left !important;
        font-family: var(--primary-font-medium);
        font-style: normal;
        font-weight: 900;
        font-size: xx-large;
        color: #545454 !important;
        background-image: url("../../Assets/Images/Modal/bankdeets.png");
        background-repeat: no-repeat;
        background-position: 0% 50%;
    }

    .custom-modal-body-title-branch-details {
        margin-top: 10%;
        padding-left: 4% !important;
        text-align: left !important;
        font-family: var(--primary-font-medium);
        font-style: normal;
        font-weight: 900;
        font-size: xx-large;
        color: #545454 !important;
        background-image: url("../../Assets/Images/Modal/branch.png");
        background-repeat: no-repeat;
        background-position: 1% 50%;
    }
    
    .custom-modal-body-title-branch-details-no-pic {
        margin-top: 10%;
        justify-content: center !important;
        text-align: center !important;
        font-family: var(--primary-font-medium);
        font-style: normal;
        font-weight: 900;
        font-size: xx-large;
        color: #545454 !important;
    }

    .custom-modal-body-title-branch-details-report-modal {
        margin-top: 10%;
        padding-left: 4% !important;
        padding-right: 4% !important;
        /* text-align: left !important; */
        font-family: var(--primary-font-medium);
        font-style: normal;
        font-weight: 700;
        font-size: x-large;
        color: white !important;
        background-color: #5ac8e1;
        border-radius: 5px;
        /* background-image: url("../../Assets/Images/Modal/branch.png"); */
        /* background-repeat: no-repeat; */
        background-position: 1% 50%;
    }

    .custom-modal-body-title-branch-details-report-modal-right {
        margin-top: 10%;
        padding-left: 4% !important;
        text-align: left !important;
        font-family: var(--primary-font-medium);
        font-style: normal;
        font-weight: 700;
        font-size: x-large;
        color: #5ac8e1!important;
        /* background-color: #169422; */
        /* border-radius: 5px; */
        /* background-image: url("../../Assets/Images/Modal/branch.png"); */
        /* background-repeat: no-repeat; */
        background-position: 1% 50%;
    }

    .custom-modal-body-title-daily-orders {
        display: flex;
        justify-content: center;
        margin-top: 0px;
        padding-left: 4% !important;
        text-align: left !important;
        font-family: var(--primary-font-medium);
        font-style: normal;
        font-weight: 900;
        font-size: xx-large;
        color: #5ac8e1 !important;
        /* background-image: url("../../Assets/Images/Modal/branch.png"); */
        background-color: #F9E25D;
        background-repeat: no-repeat;
        background-position: 1% 50%;
    }

    .custom-modal-sub-daily-orders {
        display: flex;
        justify-content: center;
        margin-top: 0px;
        padding-left: 4% !important;
        text-align: left !important;
        font-family: var(--primary-font-medium);
        font-style: normal;
        font-weight: 900;
        font-size: small;
        color: #5E5E5E !important;
        /* background-image: url("../../Assets/Images/Modal/branch.png"); */
        background-color: #F9E25D;
        background-repeat: no-repeat;
        background-position: 1% 50%;
    }

    .custom-modal-sub-product {
        display: flex;
        justify-content: left;
        margin-top: 0px;
        text-align: left !important;
        font-family: var(--primary-font-medium);
        font-style: normal;
        font-weight: 900;
        font-size: small;
        color: #808080 !important;
        /* background-image: url("../../Assets/Images/Modal/branch.png"); */
        background-repeat: no-repeat;
        background-position: 1% 50%;
    }

    .custom-modal-sub-product-yellow {
        display: flex;
        justify-content: left;
        margin-top: 0px;
        text-align: left !important;
        font-family: var(--primary-font-medium);
        font-style: normal;
        font-weight: 900;
        font-size: small;
        color: #0077b6 !important;
        /* background-image: url("../../Assets/Images/Modal/branch.png"); */
        background-repeat: no-repeat;
        background-position: 1% 50%;
    }

    .custom-modal-sub-product-white {
        display: flex;
        justify-content: left;
        margin-top: 0px;
        text-align: left !important;
        font-family: var(--primary-font-medium);
        font-style: normal;
        font-weight: 900;
        font-size: small;
        color: white !important;
        /* background-image: url("../../Assets/Images/Modal/branch.png"); */
        background-repeat: no-repeat;
        background-position: 1% 50%;
    }

    .custom-modal-pic-right {
        display: flex !important;
        justify-content: flex-end;
    }

    .custom-modal-pic-left {
        display: flex !important;
        justify-content: flex-start;
    }

    .custom-modal-body-title-forwarder-details {
        margin-top: 10%;
        padding-left: 4% !important;
        text-align: left !important;
        font-family: var(--primary-font-medium);
        font-style: normal;
        font-weight: 900;
        font-size: xx-large;
        color: #545454 !important;
        background-image: url("../../Assets/Images/Modal/forwarders.png");
        background-repeat: no-repeat;
        background-position: 1% 50%;
    }
    .custom-modal-body-title-expensetype-details {
        margin-top: 8%;
        padding-left: 4% !important;
        text-align: left !important;
        font-family: var(--primary-font-medium);
        font-style: normal;
        font-weight: 900;
        font-size: X-large;
        color: #545454 !important;
        background-image: url("../../Assets/Images/Modal/expensetypes.png");
        background-repeat: no-repeat;
        background-position: 1% 50%;
    }

    .custom-modal-body-title-contact-info {
        margin-top: 10%;
        padding-left: 4% !important;
        text-align: left !important;
        font-family: var(--primary-font-medium);
        font-style: normal;
        font-weight: 900;
        font-size: xx-large;
        color: #545454 !important;
        background-image: url("../../Assets/Images/Modal/contactinfo.png");
        background-repeat: no-repeat;
        background-position: 0% 50%;
    }

    .custom-modal-body-title-act {
        margin-top: 10%;
        padding-left: 4% !important;
        text-align: left !important;
        font-family: var(--primary-font-medium);
        font-style: normal;
        font-weight: 900;
        font-size: xx-large;
        color: #545454 !important;
        background-image: url("../../Assets/Images/Modal/act.png");
        background-repeat: no-repeat;
        background-position: 0% 50%;
    }

    .custom-modal-body-title-files {
        margin-top: 10%;
        padding-left: 4% !important;
        text-align: left !important;
        font-family: var(--primary-font-medium);
        font-style: normal;
        font-weight: 900;
        font-size: xx-large;
        color: #545454 !important;
        background-image: url("../../Assets/Images/Modal/files.png");
        background-repeat: no-repeat;
        background-position: 0% 50%;
    }

    .dropsearch-filter-col {
        z-index: 5;
    }

    .span {
        color: #5E5E5E;
        /* background-position: 0% 50%; */
        /* margin-top: 2vh; */
        /* display: inline-block;
        vertical-align: text-bottom; */
        top: 80%;
    }

    .head {
        border-bottom: 2px solid #B9B9B9;
        width: auto;

    }

    .equal {
        font-family: var(--primary-font-medium);
        font-style: normal;
        font-weight: 900;
        font-size: xx-large;
        color: #545454 !important;

    }

    .sc-ivTmOn {
        font-family: var(--primary-font-medium);
        font-style: normal;
        font-weight: 900;
        font-size: large;
        color: #545454 !important;
    }

    .eIoOYs {
        padding: 0% !important;
    }

    .fwKvpK {
        font-family: var(--primary-font-medium);
        font-style: normal;
        font-weight: 900;
        font-size: large;
        color: #545454 !important;
        text-transform: uppercase;
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        display: -webkit-box !important;
        display: -webkit-flex !important;
        display: -ms-flexbox !important;
        display: flex;
        -webkit-align-items: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        justify-content: center;
        color: rgba(0, 0, 0, 0.87);
        background-color: #FFFFFF;
        border-radius: 20px;
    }

    .form-select {
        display: inline-block !important;
        font-family: var(--primary-font-regular);
        border-radius: 7px !important;
        max-height: 35px !important;
        color: #5ac8e1;
        box-shadow: none !important;
        border:solid 1px #b3b3b3 !important;
        background-size: 15px !important;
        background-color: transparent;
    }
