import React, { useEffect, useState, useRef } from "react";
import { Col, Form, Row, Tab, Tabs } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
// import DatePicker from "react-datepicker";
import toast from "react-hot-toast";
import Select from "react-select";
import Navbar from "../../../Components/Navbar/Navbar";
import Table from "../../../Components/TableTemplate/Table";
import AddModal from "./Components/AddPIModal";
import DeleteModal from "../../../Components/Modals/DeleteModal";
import { DatePicker } from "antd";
import { getAllSuppliers } from "../../../Helpers/apiCalls/suppliersApi";
import {
  deleteSuppliesInvoice,
  filterSEInvoice,
} from "../../../Helpers/apiCalls/Expenses/suppliesInvoiceApi";
import {
  isAdmin,
  refreshPage,
  toastStyle,
  TokenExpiry,
  numberFormat,
  selectDropdownStyle,
  numberFormatInt,
  formatDateNoTime,
  firstDayOfMonth,
  lastDayOfMonth,
} from "../../../Helpers/Utils/Common";
import "./PurchaseInvoices.css";
import { getAllVendors } from "../../../Helpers/apiCalls/Manage/Vendors";
import Moment from "moment";
import PayPOModal from "./Components/PayPOModal";
import AddPaymentModal from "../../Purchases/Purchase Invoice/Components/AddPaymentModal";

const { RangePicker } = DatePicker;

export default function PurchaseInvoices() {
  let navigate = useNavigate();
  const [inactive, setInactive] = useState(true);
  const [suppliers, setSuppliers] = useState([]);
  const [showLoader, setShowLoader] = useState(false);
  const [paymentID, setPaymentID] = useState(null);
  const [invoiceNo, setInvoiceNo] = useState(null);
  const [se_id, setSe_id] = useState(null);
  const [bal, setBal] = useState(0);
  const [selectedRow, setSelectedRow] = useState({});
  const [openBills, setOpenBills] = useState([]);
  const [closedBills, setClosedBills] = useState([]);

  const [filterConfig, setFilterConfig] = useState({
    status: "open",
    supplier: "",
    bank: "",
    invoice_no: "",
    date_from: null,
    date_to: null,
  });

  const [grandTotal, setGrandTotal] = useState(0);
  const [totalPaidAmount, setTotalPaidAmount] = useState(0);
  const [totalBalance, setTotalBalance] = useState(0);

  const [selectedAction, setSelectedAction] = useState({});

  function handleFilterChange(e) {
    const { name, value } = e.target;
    setFilterConfig((prev) => {
      return { ...prev, [name]: value };
    });
  }

  const handleTabSelect = (tabKey) => {
    setFilterConfig((prev) => ({
      ...prev,
      status: tabKey,
      supplier: "",
      supplier_id: "",
      vendor_id: "",
      date_from: tabKey === "close" ? firstDayOfMonth() : null,
      date_to: tabKey === "close" ? lastDayOfMonth() : null,
    }));
  };

  const isInitialMount = useRef(true);
  const filterConfigKey = "supplies-suppliesExpenses-filterConfig";
  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      setFilterConfig((prev) => {
        if (window.localStorage.getItem(filterConfigKey) != null) {
          // handleTabSelect(JSON.parse(window.localStorage.getItem(filterConfigKey)).status);
          return JSON.parse(window.localStorage.getItem(filterConfigKey));
        } else {
          return { ...prev };
        }
      });
    } else {
      window.localStorage.setItem(
        filterConfigKey,
        JSON.stringify(filterConfig)
      );
    }
  }, [filterConfig]);

  useEffect(() => {
    fetchPI();
  }, [filterConfig]);

  async function fetchPI() {
    setShowLoader(true);

    const response = await filterSEInvoice(filterConfig);

    if (response.error) {
      if (response.error.response.data.status !== 404) {
        TokenExpiry(response.error.response.data.error);
      }
    } else {
      const allBills = response.data.response.map((invoice) => ({
        ...invoice,
        supplier_vendor: invoice.supplier_id
          ? invoice.supplier_name || invoice.supplier_trade_name
          : invoice.vendor_id
          ? invoice.vendor_name || invoice.vendor_trade_name
          : "N/A",
        po_no: invoice.se_receive_id,
        supplies_receive_date: invoice.supplies_receive_date
          ? formatDateNoTime(invoice.supplies_receive_date)
          : " ",
        total: numberFormat(invoice.grand_total),
        amount_paid: numberFormat(invoice.paid_amount),
        balance: numberFormat(invoice.balance),
      }));

      if (filterConfig.status === "open") {
        setOpenBills(
          allBills.sort(
            (a, b) =>
              new Date(...a.supplies_receive_date?.split("/").reverse()) -
              new Date(...b.supplies_receive_date?.split("/").reverse())
          ).reverse()
        );
        setGrandTotal(response.data.summary.total);
        setTotalPaidAmount(response.data.summary.total_paid);
        setTotalBalance(response.data.summary.total_balance);
      } else {
        setClosedBills(
          allBills.sort(
            (a, b) =>
              new Date(...a.supplies_receive_date?.split("/").reverse()) -
              new Date(...b.supplies_receive_date?.split("/").reverse())
          ).reverse()
        );
        setGrandTotal(response.data.summary.total);
        setTotalPaidAmount(response.data.summary.total_paid);
        setTotalBalance(response.data.summary.total_balance);
      }
    }

    setShowLoader(false);
  }

  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const handleShowDeleteModal = () => setShowDeleteModal(true);
  const handleCloseDeleteModal = () => setShowDeleteModal(false);
  const [PIID, setPIID] = useState("");

  async function handleDeletePI() {
    const response = await deleteSuppliesInvoice(PIID);

    if (response.data) {
      toast.success("Supplies Invoice Deleted Successfully!", {
        style: toastStyle(),
      });
      setTimeout(() => refreshPage(), 1000);
    } else {
      toast.error("Error Deleting Supplies Invoice", {
        style: toastStyle(),
      });
    }
  }

  const [showAddModal, setShowAddModal] = useState(false);
  const handleShowAddModal = () => setShowAddModal(true);
  const handleCloseAddModal = () => {
    refreshPage()
    // setShowAddModal(false)
  };

  /* add payment modal handler */
  const [showAddPaymentModal, setShowAddPaymentModal] = useState(false);
  const handleShowAddPaymentModal = () => setShowAddPaymentModal(true);
  const handleCloseAddPaymentModal = () => {
    setShowAddPaymentModal(false);
    refreshPage();
  };

  function handleSelectChange(e, id) {
    let selectedInvoice =
      filterConfig.status === "open"
        ? openBills.find((invoice) => invoice.id === id)
        : closedBills.find((invoice) => invoice.id === id);
  
    if (e.target.value === "edit-pi") {
      navigate("edit/" + id);
    } else if (e.target.value === "print-pi") {
      window.open("purchaseinvoices/print/" + id, "_blank");
    } else if (e.target.value === "pay-pi") {
      navigate("pay-check");
    } else if (e.target.value === "delete-pi") {
      setPIID(id);
      handleShowDeleteModal();
    } else if (e.target.value === "add-payment") {
      if (selectedInvoice) {
        setBal(numberFormatInt(selectedInvoice.balance));
        setInvoiceNo(selectedInvoice.invoice_no);
        setPaymentID(selectedInvoice.id);
        setSe_id(selectedInvoice.se_id);
        setSelectedRow(selectedInvoice);
        handleShowAddPaymentModal();
      } else {
        console.error("Invoice not found for ID:", id);
      }
    }
  
    setSelectedAction((prev) => ({ ...prev, [id]: "" }));
  }
  

  function ActionBtn(row, type) {
    return (
      <Form.Select
        name="action"
        className="PO-select-action"
        onChange={(e) => handleSelectChange(e, row.id)}
        value={selectedAction[row.id] || ""}
      >
        <option value="" hidden>
          Select
        </option>
        {isAdmin ? (
          <option value="edit-pi" className="color-options">
            Edit
          </option>
        ) : null}
        <option value="print-pi" className="color-options">
          View
        </option>
        {type !== "closed" && (
          <option value="add-payment" className="color-options">
            Add Payment
          </option>
        )}
        {isAdmin && (
          <option value="delete-pi" className="color-red">
            Delete
          </option>
        )}
        {/* <option value="add-payment-check" className="color-options">
          Pay Check
        </option>
        <option value="add-payment-bank" className="color-options">
          Pay Bank Transfer
        </option> */}
      </Form.Select>
    );
  }

  const [supplierList, setSupplierList] = useState([]);
  const [selectedSupplier, setSelectedSupplier] = useState("");

  useEffect(() => {
    setSupplierList(
      suppliers.map((supplier) => {
        return {
          label: supplier.trade_name,
          value: supplier.id + "|" + supplier.type,
        };
      })
    );
    setSupplierList((branches) => {
      var newBranches = [...branches];
      newBranches.push({ label: "All Suppliers", value: "" });
      return newBranches.reverse();
    });
  }, [suppliers]);

  function handleSupplierChange(e) {
    setSelectedSupplier(e.name);
    const toFilter = { target: { name: "supplier", value: e.value } };
    handleFilterChange(toFilter);
  }

  async function fetchSuppliers() {
    setShowLoader(true);

    const response = await getAllSuppliers();
    const response2 = await getAllVendors();
    var allData = [];

    if (response.error) {
      if (response.error.response.data.status !== 404) {
        TokenExpiry(response.error.response.data.error);
      }
    } else {
      response.data.data.map((supplier) => {
        var info = supplier;
        info.type = "supplier";
        allData.push(info);
      });
    }

    if (response2.error) {
      if (response2.error.data.status !== 404) {
        TokenExpiry(response2.error.data.status);
      }
      // TokenExpiry(response2.error);
    } else {
      response2.response.data.map((vendor) => {
        var info = vendor;
        info.type = "vendor";
        allData.push(info);
      });
    }
    setSuppliers(allData);
    setShowLoader(false);
  }

  React.useEffect(() => {
    fetchPI();
    fetchSuppliers();
  }, [filterConfig]);

  return (
    <div className="page-container">
      <div className="page">
        <Navbar
          onCollapse={(inactive) => {
            setInactive(inactive);
          }}
          active={"EXPENSE"}
        />
      </div>

      <div className={`manager-container ${inactive ? "inactive" : "active"}`}>
        <Row className="mb-4 align-items-start">
          <Col xs={6}>
            <h1 className="page-title"> SUPPLIES INVOICE </h1>
            <h5 className="page-subtitle"> Supplies Expenses</h5>
          </Col>
          <Col xs={6} className="d-flex justify-content-end">
            <input
              type="search"
              name="invoice_no"
              placeholder="Search Invoice No.."
              value={filterConfig.invoice_no}
              onChange={(e) => handleFilterChange(e)}
              className="search-bar"
            />
            <button className="add-btn" onClick={handleShowAddModal}>
              Add
            </button>
          </Col>
        </Row>

        <Tabs
          activeKey={filterConfig.status}
          defaultActiveKey={filterConfig.status}
          id="PO-tabs"
          onSelect={handleTabSelect}
        >
          <Tab eventKey="open" title="Open Bills" className="sePI-tab-wrapper">
            <div className="my-2 ms-2 PO-filters PI-filters d-flex">
              <Select
                className="dropsearch-filter px-0 py-0 me-2"
                classNamePrefix="react-select"
                placeholder="Select Supplier"
                styles={selectDropdownStyle}
                value={selectedSupplier}
                options={supplierList}
                onChange={handleSupplierChange}
              />
              <RangePicker 
                placeholder={[
                  filterConfig.date_from ?? firstDayOfMonth(), 
                  filterConfig.date_to ?? lastDayOfMonth()
                ]} 
                onChange={(e) => {
                  if (e) {
                    setFilterConfig((prev) => ({
                      ...prev,
                      date_from: e[0].format('YYYY-MM-DD'),
                      date_to: e[1].format('YYYY-MM-DD'),
                    }))
                  } else {
                    setFilterConfig((prev) => ({
                      ...prev,
                      date_from: firstDayOfMonth(),
                      date_to: lastDayOfMonth(),
                    }))
                  }
                }}
              />
            </div>

            <div className=" PO-filters d-flex justify-content-center">
              <span className="me-4 ml-4 mt-2 ps-label">
                Grand Total: {numberFormat(grandTotal)}
              </span>

              <span className="me-4 ml-4 mt-2 ps-label">
                Total Paid Amount: {numberFormat(totalPaidAmount)}
              </span>

              <span className="me-4 ml-4 mt-2 ps-label">
                Total Balance: {numberFormat(totalBalance)}
              </span>
            </div>

            <div className="sePI-tbl ">
              {
                <Table
                  tableHeaders={[
                    // "-",
                    "DATE",
                    "SUPPLIER",
                    "INV NO.",
                    "REMARKS",
                    "TOTAL",
                    "PAID AMT",
                    "BALANCE",
                    "PYMT REF NO",
                    "SE NO.",
                    "PREP BY",
                    "ACTIONS",
                  ]}
                  headerSelector={[
                    // "-",
                    "supplies_receive_date",
                    "supplier_vendor",
                    "invoice_no",
                    "remarks",
                    "total",
                    "amount_paid",
                    "balance",
                    "reference_no",
                    "po_no",
                    "prepared_by",
                  ]}
                  tableData={openBills}
                  ActionBtn={(row) => ActionBtn(row, "open")}
                  // ViewBtn={(row) => ViewPIBtn(row)}
                  showLoader={showLoader}
                />
              }
            </div>
            <div className="mb-2" />
          </Tab>

          <Tab
            eventKey="close"
            title="Closed Bills"
            className="sePI-tab-wrapper"
          >
            {/* filters */}

            <div className="my-2 ms-2 PO-filters PI-filters d-flex">
              <Select
                className="dropsearch-filter px-0 py-0 me-2"
                classNamePrefix="react-select"
                placeholder="Select Supplier"
                styles={selectDropdownStyle}
                value={selectedSupplier}
                options={supplierList}
                onChange={handleSupplierChange}
              />
              <RangePicker 
                placeholder={[
                  filterConfig.date_from ?? firstDayOfMonth(), 
                  filterConfig.date_to ?? lastDayOfMonth()
                ]} 
                onChange={(e) => {
                  if (e) {
                    setFilterConfig((prev) => ({
                      ...prev,
                      date_from: e[0].format('YYYY-MM-DD'),
                      date_to: e[1].format('YYYY-MM-DD'),
                    }))
                  } else {
                    setFilterConfig((prev) => ({
                      ...prev,
                      date_from: firstDayOfMonth(),
                      date_to: lastDayOfMonth(),
                    }))
                  }
                }}
              />
            </div>

            <div className=" PO-filters d-flex justify-content-center">
              <span className="me-4 ml-4 mt-2 ps-label">
                Grand Total: {numberFormat(grandTotal)}
              </span>

              <span className="me-4 ml-4 mt-2 ps-label">
                Total Paid Amount: {numberFormat(totalPaidAmount)}
              </span>

              <span className="me-4 ml-4 mt-2 ps-label">
                Total Balance: {numberFormat(totalBalance)}
              </span>
            </div>

            <div className="">
              <Table
                tableHeaders={[
                  // "-",
                  "DATE",
                  "SUPPLIER",
                  "INV NO.",
                  "REMARKS",
                  "TOTAL",
                  "PAID AMT",
                  "BALANCE",
                  "PYMT REF NO",
                  "SE NO.",
                  "PREP BY",
                  "ACTIONS",
                ]}
                headerSelector={[
                  // "-",
                  "supplies_receive_date",
                  "supplier_vendor",
                  "invoice_no",
                  "remarks",
                  "total",
                  "amount_paid",
                  "balance",
                  "reference_no",
                  "po_no",
                  "prepared_by",
                ]}
                tableData={closedBills}
                ActionBtn={(row) => ActionBtn(row, "closed")}
                // ViewBtn={(row) => ViewPIBtn(row)}
                showLoader={showLoader}
              />
            </div>
            <div className="mb-2" />
          </Tab>
        </Tabs>
      </div>
      <DeleteModal
        show={showDeleteModal}
        onHide={() => handleCloseDeleteModal()}
        text="purchase invoice"
        onDelete={() => handleDeletePI()}
      />
      <AddModal show={showAddModal} hide={handleCloseAddModal} />
      {/* <PayPOModal
        id={paymentID}
        show={showAddPaymentModal}
        onHide={handleCloseAddPaymentModal}
        balance={bal}
        invoice={selectedRow}
        invoice_no = {invoiceNo}
      /> */}
      <AddPaymentModal
        id={paymentID}
        se_id={se_id}
        invoice_no={invoiceNo}
        show={showAddPaymentModal}
        onHide={handleCloseAddPaymentModal}
        balance={bal}
        invoice={selectedRow}
      />
    </div>
  );
}
