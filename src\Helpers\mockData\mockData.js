export const suppliersMockData = [
  { company_name: "Amazing, Inc.", address: "Cebu City", contact_person: "<PERSON><PERSON>", phone_number: "***********" },
  { company_name: "mazing, Inc.", address: "Cebu City", contact_person: "<PERSON><PERSON>", phone_number: "***********" },
  { company_name: "Amazing, Inc.", address: "Cebu City", contact_person: "<PERSON><PERSON>", phone_number: "***********" },
  { company_name: "mazing, Inc.", address: "Cebu City", contact_person: "<PERSON><PERSON>", phone_number: "***********" },
  { company_name: "Amazing, Inc.", address: "Cebu City", contact_person: "<PERSON><PERSON>", phone_number: "***********" },
  { company_name: "mazing, Inc.", address: "Cebu City", contact_person: "<PERSON><PERSON>", phone_number: "***********" },
  { company_name: "Amazing, Inc.", address: "Cebu City", contact_person: "<PERSON><PERSON>", phone_number: "***********" },
  { company_name: "mazing, Inc.", address: "Cebu City", contact_person: "<PERSON><PERSON>", phone_number: "***********" },
  { company_name: "Amazing, Inc.", address: "Cebu City", contact_person: "<PERSON>a <PERSON>", phone_number: "***********" },
  { company_name: "mazing, Inc.", address: "Cebu City", contact_person: "Juana <PERSON>", phone_number: "***********" },
  { company_name: "Amazing, Inc.", address: "Cebu City", contact_person: "Juana <PERSON>", phone_number: "***********" },
  { company_name: "mazing, Inc.", address: "Cebu City", contact_person: "Juana Marie", phone_number: "***********" },
  { company_name: "Amazing, Inc.", address: "Cebu City", contact_person: "Juana Marie", phone_number: "***********" },
  { company_name: "mazing, Inc.", address: "Cebu City", contact_person: "Juana Marie", phone_number: "***********" },
  { company_name: "Amazing, Inc.", address: "Cebu City", contact_person: "Juana Marie", phone_number: "***********" },
  { company_name: "mazing, Inc.", address: "Cebu City", contact_person: "Juana Marie", phone_number: "***********" },
];

export const banksMockData = [
  { bank_name: "BDO", print_template: "BDO (Check only)" },
  { bank_name: "BPI", print_template: "BDO (Check only)" },
  { bank_name: "BPI", print_template: "BDO (Check only)" },
  { bank_name: "BDO", print_template: "BDO (Check only)" },
  { bank_name: "BPI", print_template: "BDO (Check only)" },
  { bank_name: "BDO", print_template: "BDO (Check only)" },
  { bank_name: "BDO", print_template: "BDO (Check only)" },
  { bank_name: "BPI", print_template: "BDO (Check only)" },
  { bank_name: "BDO", print_template: "BDO (Check only)" },
  { bank_name: "AUB", print_template: "BDO (Check only)" },
  { bank_name: "AUB", print_template: "BDO (Check only)" },
  { bank_name: "ChinaBank", print_template: "BDO (Check only)" },

];

export const forwardersMockData = [
  { name: "J&T Express", address: "Cebu City"},
  { name: "LBC Express", address: "Cebu City"},
  { name: "2GO Express", address: "Cebu City"},
  { name: "ENTREGO", address: "Cebu City"},
  { name: "AIR21", address: "Cebu City"},
  { name: "DHL", address: "Cebu City"},
  { name: "LALAMOVE", address: "Cebu City"},
  { name: "FEDEX", address: "Cebu City"},
]

export const branchesMockData = [
  { branch_name: "SM City Cebu" },
  { branch_name: "Ayala Center Cebu" },
  { branch_name: "SM Seaside City" },
  { branch_name: "SM Tacloban" },
  { branch_name: "Parkmall" },
  { branch_name: "Metro Colon" },
  { branch_name: "SM Consolacion" },

];

export const itemsMockData = [
  { item_name: "Mango" },
  { item_name: "Sugar" },
  { item_name: "Plastic Cup" },
  { item_name: "Straw" },
  { item_name: "Condensed Milk" },
  { item_name: "Evaporated Milk" },
  { item_name: "Plastic Spoon" },
  { item_name: "Vanilla Extract" },
  { item_name: "Vanilla Ice Cream" },
  { item_name: "Chocolate Ice Cream" },
  { item_name: "Mango" },
  { item_name: "Mango" },
]

export const productsMockData = [
  { product_name: "Fresh Mango Shake" },
  { product_name: "Mango Fling" },
  { product_name: "Vanilla Mango Float Shake" },
  { product_name: "Ube Mango Float Shake" },
  { product_name: "Chocolate Mango Float Shake" },
  { product_name: "Dark Chocolate Mango Float Shake" },
  { product_name: "Strawberry Mango Float Shake" },
  { product_name: "Banana Mango Float Shake" },
  { product_name: "Mango Juice" },
  { product_name: "Homemade Ice Cream" },
  { product_name: "Mango" },
  { product_name: "Mango" },
]

export const usersMockData = [
  { full_name: "Juana Marie", username: "Juana1234", role: "Admin" },
  { full_name: "Juana Marie", username: "Juana1234", role: "Admin" },
  { full_name: "Juana Marie", username: "Juana1234", role: "Staff" },
  { full_name: "Juana Marie", username: "Juana1234", role: "Admin" },
  { full_name: "Juana Marie", username: "Juana1234", role: "Manager" },
  { full_name: "Juana Marie", username: "Juana1234", role: "Manager" },
  { full_name: "Juana Marie", username: "Juana1234", role: "Admin" },
  { full_name: "Juana Marie", username: "Juana1234", role: "Staff" },
  { full_name: "Juana Marie", username: "Juana1234", role: "Admin" },
  { full_name: "Juana Marie", username: "Juana1234", role: "Admin" },
  { full_name: "Juana Marie", username: "Juana1234", role: "Manager" },
  { full_name: "Juana Marie", username: "Juana1234", role: "Manager" },
]

export const employeesMockData = [
  { full_name: "Juana Marie", username: "Juana1234", type: "Admin" },
  { full_name: "Juana Marie", username: "Juana1234", type: "Admin" },
  { full_name: "Juana Marie", username: "Juana1234", type: "Admin" },
  { full_name: "Juana Marie", username: "Juana1234", type: "Admin" },
  { full_name: "Juana Marie", username: "Juana1234", type: "Admin" },
  { full_name: "Juana Marie", username: "Juana1234", type: "Admin" },
  { full_name: "Juana Marie", username: "Juana1234", type: "Admin" },
  { full_name: "Juana Marie", username: "Juana1234", type: "Admin" },
  { full_name: "Juana Marie", username: "Juana1234", type: "Admin" },
  { full_name: "Juana Marie", username: "Juana1234", type: "Admin" },
  { full_name: "Juana Marie", username: "Juana1234", type: "Admin" },
  { full_name: "Juana Marie", username: "Juana1234", type: "Admin" }
]

export const sampleItems = [
  { item: 'sulfur soap', qty: 2, unit: 'cup', price: 25, amount: 50 },
  { item: 'sulfur soap2', qty: 3, unit: 'cup', price: 23, amount: 69 }
]

export const POpending = [
  { po_no: 1, purchase_date: '07/12/2022', supplier: 'Princess Atillo', total: '1200', payment_status: 'paid', order_status: 'delivered', check: 203, prepared: 'Jane Doe', approved: 'Jas Doe', printed: 'Joe Doe' },
  { po_no: 2, purchase_date: '07/12/2022', supplier: 'Ellenmarie Puyot', total: '1200', payment_status: 'paid', order_status: 'delivered', check: 203, prepared: 'Jane Doe', approved: 'Jas Doe', printed: 'Joe Doe' },
  { po_no: 3, purchase_date: '07/12/2022', supplier: 'Daughpane Reponte', total: '1200', payment_status: 'paid', order_status: 'delivered', check: 203, prepared: 'Jane Doe', approved: 'Jas Doe', printed: 'Joe Doe' },
  { po_no: 4, purchase_date: '07/12/2022', supplier: 'Gioly Bacang', total: '1200', payment_status: 'paid', order_status: 'delivered', check: 203, prepared: 'Jane Doe', approved: 'Jas Doe', printed: 'Joe Doe' },
  { po_no: 5, purchase_date: '07/12/2022', supplier: 'Priscilla Pearson', total: '1200', payment_status: 'paid', order_status: 'delivered', check: 203, prepared: 'Jane Doe', approved: 'Jas Doe', printed: 'Joe Doe' },
  { po_no: 6, purchase_date: '07/12/2022', supplier: 'Jennifer Aniston', total: '1200', payment_status: 'paid', order_status: 'delivered', check: 203, prepared: 'Jane Doe', approved: 'Jas Doe', printed: 'Joe Doe' },
  { po_no: 7, purchase_date: '07/12/2022', supplier: 'Joey Tribbiani', total: '1200', payment_status: 'paid', order_status: 'delivered', check: 203, prepared: 'Jane Doe', approved: 'Jas Doe', printed: 'Joe Doe' },
  { po_no: 8, purchase_date: '07/12/2022', supplier: 'Monica Geller', total: '1200', payment_status: 'paid', order_status: 'delivered', check: 203, prepared: 'Jane Doe', approved: 'Jas Doe', printed: 'Joe Doe' },
  { po_no: 9, purchase_date: '07/12/2022', supplier: 'Ross Geller', total: '1200', payment_status: 'paid', order_status: 'delivered', check: 203, prepared: 'Jane Doe', approved: 'Jas Doe', printed: 'Joe Doe' },
  { po_no: 10, purchase_date: '07/12/2022', supplier: 'Chandler Bing', total: '1200', payment_status: 'paid', order_status: 'delivered', check: 203, prepared: 'Jane Doe', approved: 'Jas Doe', printed: 'Joe Doe' },
  { po_no: 1, purchase_date: '07/12/2022', supplier: 'Princess Atillo', total: '1200', payment_status: 'paid', order_status: 'delivered', check: 203, prepared: 'Jane Doe', approved: 'Jas Doe', printed: 'Joe Doe' },
  { po_no: 2, purchase_date: '07/12/2022', supplier: 'Ellen Puyot', total: '1200', payment_status: 'paid', order_status: 'delivered', check: 203, prepared: 'Jane Doe', approved: 'Jas Doe', printed: 'Joe Doe' },
  { po_no: 3, purchase_date: '07/12/2022', supplier: 'Daughpane Reponte', total: '1200', payment_status: 'paid', order_status: 'delivered', check: 203, prepared: 'Jane Doe', approved: 'Jas Doe', printed: 'Joe Doe' },
  { po_no: 4, purchase_date: '07/12/2022', supplier: 'Gioly Bacang', total: '1200', payment_status: 'paid', order_status: 'delivered', check: 203, prepared: 'Jane Doe', approved: 'Jas Doe', printed: 'Joe Doe' },
  { po_no: 5, purchase_date: '07/12/2022', supplier: 'Priscilla Pearson', total: '1200', payment_status: 'paid', order_status: 'delivered', check: 203, prepared: 'Jane Doe', approved: 'Jas Doe', printed: 'Joe Doe' },
  { po_no: 6, purchase_date: '07/12/2022', supplier: 'Jennifer Aniston', total: '1200', payment_status: 'paid', order_status: 'delivered', check: 203, prepared: 'Jane Doe', approved: 'Jas Doe', printed: 'Joe Doe' },
  { po_no: 7, purchase_date: '07/12/2022', supplier: 'Joey Tribbiani', total: '1200', payment_status: 'paid', order_status: 'delivered', check: 203, prepared: 'Jane Doe', approved: 'Jas Doe', printed: 'Joe Doe' },
  { po_no: 8, purchase_date: '07/12/2022', supplier: 'Monica Geller', total: '1200', payment_status: 'paid', order_status: 'delivered', check: 203, prepared: 'Jane Doe', approved: 'Jas Doe', printed: 'Joe Doe' },
  { po_no: 9, purchase_date: '07/12/2022', supplier: 'Ross Geller', total: '1200', payment_status: 'paid', order_status: 'delivered', check: 203, prepared: 'Jane Doe', approved: 'Jas Doe', printed: 'Joe Doe' },
  { po_no: 10, purchase_date: '07/12/2022', supplier: 'Chandler Bing', total: '1200', payment_status: 'paid', order_status: 'delivered', check: 203, prepared: 'Jane Doe', approved: 'Jas Doe', printed: 'Joe Doe' }
]

export const purchaseInvoice = [
  { invoice: 1, date: '07/12/2022', supplier: 'Joe Dela Cruz', payment_status: "PAID", total: "1200", amount_paid: "300", balance: "900", dr_no: 210, check_no: 54631, po_no: 569812 },
  { invoice: 1, date: '07/12/2022', supplier: 'Joe Dela Cruz', payment_status: "PAID", total: "1200", amount_paid: "300", balance: "900", dr_no: 210, check_no: 54631, po_no: 569812 },
  { invoice: 1, date: '07/12/2022', supplier: 'Joe Dela Cruz', payment_status: "PAID", total: "1200", amount_paid: "300", balance: "900", dr_no: 210, check_no: 54631, po_no: 569812 },
  { invoice: 1, date: '07/12/2022', supplier: 'Joe Dela Cruz', payment_status: "PAID", total: "1200", amount_paid: "300", balance: "900", dr_no: 210, check_no: 54631, po_no: 569812 },
  { invoice: 1, date: '07/12/2022', supplier: 'Joe Dela Cruz', payment_status: "PAID", total: "1200", amount_paid: "300", balance: "900", dr_no: 210, check_no: 54631, po_no: 569812 },
  { invoice: 1, date: '07/12/2022', supplier: 'Joe Dela Cruz', payment_status: "PAID", total: "1200", amount_paid: "300", balance: "900", dr_no: 210, check_no: 54631, po_no: 569812 },
  { invoice: 1, date: '07/12/2022', supplier: 'Joe Dela Cruz', payment_status: "PAID", total: "1200", amount_paid: "300", balance: "900", dr_no: 210, check_no: 54631, po_no: 569812 },
  { invoice: 1, date: '07/12/2022', supplier: 'Joe Dela Cruz', payment_status: "PAID", total: "1200", amount_paid: "300", balance: "900", dr_no: 210, check_no: 54631, po_no: 569812 },
  { invoice: 1, date: '07/12/2022', supplier: 'Joe Dela Cruz', payment_status: "PAID", total: "1200", amount_paid: "300", balance: "900", dr_no: 210, check_no: 54631, po_no: 569812 },
  { invoice: 1, date: '07/12/2022', supplier: 'Joe Dela Cruz', payment_status: "PAID", total: "1200", amount_paid: "300", balance: "900", dr_no: 210, check_no: 54631, po_no: 569812 },
  { invoice: 1, date: '07/12/2022', supplier: 'Joe Dela Cruz', payment_status: "PAID", total: "1200", amount_paid: "300", balance: "900", dr_no: 210, check_no: 54631, po_no: 569812 },
  { invoice: 1, date: '07/12/2022', supplier: 'Joe Dela Cruz', payment_status: "PAID", total: "1200", amount_paid: "300", balance: "900", dr_no: 210, check_no: 54631, po_no: 569812 },
  { invoice: 1, date: '07/12/2022', supplier: 'Joe Dela Cruz', payment_status: "PAID", total: "1200", amount_paid: "300", balance: "900", dr_no: 210, check_no: 54631, po_no: 569812 },
  { invoice: 1, date: '07/12/2022', supplier: 'Joe Dela Cruz', payment_status: "PAID", total: "1200", amount_paid: "300", balance: "900", dr_no: 210, check_no: 54631, po_no: 569812 },
  { invoice: 1, date: '07/12/2022', supplier: 'Joe Dela Cruz', payment_status: "PAID", total: "1200", amount_paid: "300", balance: "900", dr_no: 210, check_no: 54631, po_no: 569812 },
  { invoice: 1, date: '07/12/2022', supplier: 'Joe Dela Cruz', payment_status: "PAID", total: "1200", amount_paid: "300", balance: "900", dr_no: 210, check_no: 54631, po_no: 569812 },
  { invoice: 1, date: '07/12/2022', supplier: 'Joe Dela Cruz', payment_status: "PAID", total: "1200", amount_paid: "300", balance: "900", dr_no: 210, check_no: 54631, po_no: 569812 },
  { invoice: 1, date: '07/12/2022', supplier: 'Joe Dela Cruz', payment_status: "PAID", total: "1200", amount_paid: "300", balance: "900", dr_no: 210, check_no: 54631, po_no: 569812 },
  { invoice: 1, date: '07/12/2022', supplier: 'Joe Dela Cruz', payment_status: "PAID", total: "1200", amount_paid: "300", balance: "900", dr_no: 210, check_no: 54631, po_no: 569812 },
  { invoice: 1, date: '07/12/2022', supplier: 'Joe Dela Cruz', payment_status: "PAID", total: "1200", amount_paid: "300", balance: "900", dr_no: 210, check_no: 54631, po_no: 569812 },
]

export const addPurchasInvoice = [
  {item: 'Pearl', qty: '2', unit: 'Pack', price: '25', total: '50', ordered: '20', prev: '0', balance: '23'},
  {item: 'Pearl', qty: '2', unit: 'Pack', price: '25', total: '50', ordered: '20', prev: '0', balance: '23'}
]

export const paySuppliersMockData = [
  {date: "07-21-2022", issue_date: "07-19-2022", doc_no: "********", bank_name: "BPI", payment_mode: "CHECK", supplier: "Amazing, Inc.", payee: "Juana Marie", amount: "21,150.00"},
  {date: "07-21-2022", issue_date: "07-19-2022", doc_no: "********", bank_name: "BPI", payment_mode: "CHECK", supplier: "Amazing, Inc.", payee: "Juana Marie", amount: "21,150.00"},
  {date: "07-21-2022", issue_date: "07-19-2022", doc_no: "********", bank_name: "BPI", payment_mode: "CHECK", supplier: "Amazing, Inc.", payee: "Juana Marie", amount: "21,150.00"},
  {date: "07-21-2022", issue_date: "07-19-2022", doc_no: "********", bank_name: "BPI", payment_mode: "CHECK", supplier: "Amazing, Inc.", payee: "Juana Marie", amount: "21,150.00"},
  {date: "07-21-2022", issue_date: "07-19-2022", doc_no: "********", bank_name: "BPI", payment_mode: "CHECK", supplier: "Amazing, Inc.", payee: "Juana Marie", amount: "21,150.00"},
  {date: "07-21-2022", issue_date: "07-19-2022", doc_no: "********", bank_name: "BPI", payment_mode: "CHECK", supplier: "Amazing, Inc.", payee: "Juana Marie", amount: "21,150.00"},
  {date: "07-21-2022", issue_date: "07-19-2022", doc_no: "********", bank_name: "BDO", payment_mode: "BANK TRANSFER", supplier: "Amazing, Inc.", payee: "Juana Marie", amount: "21,150.00"},
  {date: "07-21-2022", issue_date: "07-19-2022", doc_no: "********", bank_name: "BDO", payment_mode: "BANK TRANSFER", supplier: "Amazing, Inc.", payee: "Juana Marie", amount: "21,150.00"},
  {date: "07-21-2022", issue_date: "07-19-2022", doc_no: "********", bank_name: "BDO", payment_mode: "BANK TRANSFER", supplier: "Amazing, Inc.", payee: "Juana Marie", amount: "21,150.00"},
  {date: "07-21-2022", issue_date: "07-19-2022", doc_no: "********", bank_name: "BDO", payment_mode: "BANK TRANSFER", supplier: "Amazing, Inc.", payee: "Juana Marie", amount: "21,150.00"},
  {date: "07-21-2022", issue_date: "07-19-2022", doc_no: "********", bank_name: "BDO", payment_mode: "BANK TRANSFER", supplier: "Amazing, Inc.", payee: "Juana Marie", amount: "21,150.00"},
  {date: "07-21-2022", issue_date: "07-19-2022", doc_no: "********", bank_name: "BDO", payment_mode: "BANK TRANSFER", supplier: "Amazing, Inc.", payee: "Juana Marie", amount: "21,150.00"},
  {date: "07-21-2022", issue_date: "07-19-2022", doc_no: "********", bank_name: "", payment_mode: "CASH", supplier: "Amazing, Inc.", payee: "Juana Marie", amount: "21,150.00"},
  {date: "07-21-2022", issue_date: "07-19-2022", doc_no: "********", bank_name: "", payment_mode: "CASH", supplier: "Amazing, Inc.", payee: "Juana Marie", amount: "21,150.00"},
  {date: "07-21-2022", issue_date: "07-19-2022", doc_no: "********", bank_name: "", payment_mode: "CASH", supplier: "Amazing, Inc.", payee: "Juana Marie", amount: "21,150.00"},
  {date: "07-21-2022", issue_date: "07-19-2022", doc_no: "********", bank_name: "", payment_mode: "CASH", supplier: "Amazing, Inc.", payee: "Juana Marie", amount: "21,150.00"},
  {date: "07-21-2022", issue_date: "07-19-2022", doc_no: "********", bank_name: "", payment_mode: "CASH", supplier: "Amazing, Inc.", payee: "Juana Marie", amount: "21,150.00"},
  {date: "07-21-2022", issue_date: "07-19-2022", doc_no: "********", bank_name: "", payment_mode: "CASH", supplier: "Amazing, Inc.", payee: "Juana Marie", amount: "21,150.00"},
  {date: "07-21-2022", issue_date: "07-19-2022", doc_no: "********", bank_name: "", payment_mode: "CASH", supplier: "Amazing, Inc.", payee: "Juana Marie", amount: "21,150.00"},
  {date: "07-21-2022", issue_date: "07-19-2022", doc_no: "********", bank_name: "", payment_mode: "CASH", supplier: "Amazing, Inc.", payee: "Juana Marie", amount: "21,150.00"},
]