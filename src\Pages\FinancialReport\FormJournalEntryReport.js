import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Col, Form, Row, Table } from "react-bootstrap";
import { useNavigate, useParams } from "react-router-dom";
import Select from "react-select";
import { DatePicker } from "antd";
import Navbar from "../../Components/Navbar/Navbar";
import { getAllProjects } from "../../Helpers/apiCalls/Purchases/purchaseOrderApi";
import { getAllExpenseType } from "../../Helpers/apiCalls/expensetypesApi";
import { createJournalEntry, getJournalEntry, editJournalEntry as updateJournalEntry } from "../../Helpers/apiCalls/SalesReport/SalesReportApi";
import trash from "../../Assets/Images/trash.png";
import dayjs from "dayjs";
import InputError from "../../Components/InputError/InputError";
import { toast } from "react-toastify";
import { formatDateNoTime, numberFormat } from "../../Helpers/Utils/Common";

function FormJournalEntry({ add, edit, view }) {
  const navigate = useNavigate();
  const { id } = useParams();
  const [inactive, setInactive] = useState(true);
  const [projects, setProjects] = useState([]);
  const [accountTypes, setAccountTypes] = useState([]);
  const [formDetails, setFormDetails] = useState({
    id: null,
    date: "",
    remarks: "",
    ref_no: "",
  });
  const [journalEntries, setJournalEntries] = useState([
    {
      id: null,
      account_type: "",
      debit: 0,
      credit: 0,
      remarks: "",
      project_id: null,
    },
  ]);
  const [isError, setIsError] = useState({
    date: false,
    journalEntries: false,
  });
  const [totals, setTotals] = useState({
    totalDebit: 0,
    totalCredit: 0,
  });
  const [isBalanced, setIsBalanced] = useState(true);

  // Fetch all projects for the dropdown
  async function fetchAllProjects() {
    const response = await getAllProjects();
    if (response?.data?.data) {
      const formattedProjects = response.data.data.map((project) => ({
        label: project.name,
        value: project.id,
      }));
      setProjects(formattedProjects);
    }
  }

  // Fetch all account types for the dropdown
  async function fetchAccountTypes() {
    const response = await getAllExpenseType();
    if (response?.data) {
      const formattedAccountTypes = response.data.map((type) => ({
        label: type.name,
        value: type.id,
      }));
      setAccountTypes(formattedAccountTypes);
    }
  }

  // Fetch journal entry data for view/edit mode
  async function fetchJournalEntryData() {
    try {
      if (!id) {
        toast.error("Invalid journal entry ID.");
        return;
      }

      const response = await getJournalEntry(id);

      if (response?.data?.status === "success" && response?.data?.data?.[0]) {
        const entryData = response.data.data[0];

        setFormDetails({
          id: id,
          date: entryData.date,
          remarks: entryData.remarks || "",
          ref_no: entryData.ref_no || "",
        });

        if (Array.isArray(entryData.journal_entry_items)) {
          const formattedEntries = entryData.journal_entry_items.map((entry) => ({
            id: entry.id,
            account_type: entry.expense_type_id,
            debit: parseFloat(entry.debit || 0).toFixed(2),
            credit: parseFloat(entry.credit || 0).toFixed(2),
            remarks: entry.remarks || "",
            project_id: entry.project_id,
            expense_type_name: entry.expense_type_name,
            project_name: entry.project_name
          }));

          setJournalEntries(formattedEntries);
          
          // Calculate and set totals
          const newTotals = {
            totalDebit: parseFloat(entryData.total_debit || 0),
            totalCredit: parseFloat(entryData.total_credit || 0),
          };
          setTotals(newTotals);
        }
      } else {
        toast.error("Invalid data format received from server");
      }
    } catch (error) {
      console.error("Error fetching journal entry:", error);
      toast.error("Error fetching journal entry data");
    }
  }

  // Handle form field changes
  function handleFormChange(e) {
    const { name, value } = e.target;
    setFormDetails((prevDetails) => ({
      ...prevDetails,
      [name]: value,
    }));
  }

  // Handle journal entry row changes
  function handleJournalEntryChange(e, index) {
    const { name, value } = e.target;
    const numericValue = parseFloat(value) || 0;

    setJournalEntries((prevEntries) => {
      const updatedEntries = [...prevEntries];
      
      // If entering debit, clear credit and vice versa
      if (name === "debit" && numericValue > 0) {
        updatedEntries[index] = {
          ...updatedEntries[index],
          [name]: numericValue,
          credit: 0
        };
      } else if (name === "credit" && numericValue > 0) {
        updatedEntries[index] = {
          ...updatedEntries[index],
          [name]: numericValue,
          debit: 0
        };
      } else {
        updatedEntries[index] = {
          ...updatedEntries[index],
          [name]: numericValue
        };
      }

      // Calculate new totals
      const newTotals = updatedEntries.reduce(
        (acc, entry) => ({
          totalDebit: acc.totalDebit + parseFloat(entry.debit || 0),
          totalCredit: acc.totalCredit + parseFloat(entry.credit || 0),
        }),
        { totalDebit: 0, totalCredit: 0 }
      );

      setTotals(newTotals);
      // Check if debits and credits are balanced
      setIsBalanced(Math.abs(newTotals.totalDebit - newTotals.totalCredit) < 0.01);

      return updatedEntries;
    });
  }

  // Handle project dropdown change
  function handleProjectChange(selectedOption, index) {
    setJournalEntries((prevEntries) => {
      const updatedEntries = [...prevEntries];
      updatedEntries[index] = {
        ...updatedEntries[index],
        project_id: selectedOption.value,
      };
      return updatedEntries;
    });
  }

  // Add a new journal entry row
  function addJournalEntryRow() {
    setJournalEntries((prev) => [
      ...prev,
      { account_type: "", debit: 0, credit: 0, remarks: "", project_id: null },
    ]);
  }

  // Delete a journal entry row
  function deleteJournalEntryRow(index) {
    setJournalEntries((prev) => prev.filter((_, i) => i !== index));
  }

  async function saveNewSE() {
    if (!validateForm()) return;

    try {
      // Format the payload to match backend expectations
      const payload = {
        date: formDetails.date,
        remarks: formDetails.remarks,
        ref_no: formDetails.ref_no,
        total_debit: totals.totalDebit,
        total_credit: totals.totalCredit,
        project_ids: journalEntries.map((entry) => entry.project_id),
        expense_type_ids: journalEntries.map((entry) => entry.account_type),
        debits: journalEntries.map((entry) => parseFloat(entry.debit || 0)),
        credits: journalEntries.map((entry) => parseFloat(entry.credit || 0)),
        item_remarks: journalEntries.map((entry) => entry.remarks || ""),
      };

      const response = await createJournalEntry(payload);

      if (response?.data?.status === "success") {
        toast.success("Journal entry created successfully!");
        // Navigate with the new entry data formatted correctly
        navigate("/journalentryreport", {
          state: {
            newEntry: {
              date: payload.date,
              journalEntries: journalEntries.map(entry => ({
                debit: entry.debit,
                credit: entry.credit,
                remarks: entry.remarks
              }))
            }
          }
        });
      } else {
        toast.error(response?.error?.data?.response || "Failed to create journal entry");
      }
    } catch (error) {
      console.error("Error creating journal entry:", error);
      toast.error("Error creating journal entry");
    }
  }

  // Update handleSubmit function
  async function handleSubmit() {
    try {
      if (add) {
        await saveNewSE();
      } else if (edit) {
        await handleEditJournalEntry();
      }
    } catch (error) {
      toast.error("An error occurred while processing your request");
    }
  }

  // Edit journal entry
  async function handleEditJournalEntry() {
    if (!validateForm()) return;

    try {
      const payload = {
        journal_entry_id: id,
        date: formDetails.date,
        remarks: formDetails.remarks,
        ref_no: formDetails.ref_no,
        total_debit: totals.totalDebit,
        total_credit: totals.totalCredit,
        project_ids: journalEntries.map((entry) => entry.project_id),
        expense_type_ids: journalEntries.map((entry) => entry.account_type),
        debits: journalEntries.map((entry) => parseFloat(entry.debit || 0)),
        credits: journalEntries.map((entry) => parseFloat(entry.credit || 0)),
        item_remarks: journalEntries.map((entry) => entry.remarks || ""),
      };

      const response = await updateJournalEntry(payload);

      if (response?.data?.status === "success") {
        toast.success("Journal entry updated successfully!");
        navigate("/journalentryreport", {
          state: { 
            refresh: true 
          }
        });
      } else {
        toast.error(response?.error?.data?.response || "Error updating journal entry");
      }
    } catch (error) {
      console.error("Error updating journal entry:", error);
      toast.error("Error updating journal entry");
    }
  }

  // Validate form
  function validateForm() {
    const isValid = {
      date: Boolean(formDetails.date),
      journalEntries: journalEntries.length > 0,
    };

    setIsError({
      date: !isValid.date,
      journalEntries: !isValid.journalEntries,
    });

    return Object.values(isValid).every(Boolean);
  }

  useEffect(() => {
    fetchAllProjects();
    fetchAccountTypes();
  }, []); // Initial load

  useEffect(() => {
    if (id && (edit || view)) {
      fetchJournalEntryData();
      
    }
  }, [id, edit, view]);

  return (
    <div>
      <div className="page">
        <Navbar onCollapse={(inactive) => setInactive(inactive)} active="FINANCIAL STATEMENTS" />
      </div>
      <div className={`manager-container ${inactive ? "inactive" : "active"}`}>
        <div className="d-flex justify-content-between align-items-center pb-4">
          <h1 className="page-title mb-0">
            {add ? "ADD JOURNAL ENTRY" : edit ? "EDIT JOURNAL ENTRY" : "VIEW JOURNAL ENTRY"}
          </h1>
        </div>
        <div className="edit-form">
          <Row className="mt-4 mb-2">
            <Col xs={2}>
              <span className="edit-label">Date <span className="color-red">*</span></span>
              <DatePicker
                className="nc-modal-custom-text-new-datepicker"
                name="date"
                value={formDetails.date ? dayjs(formDetails.date) : null}
                onChange={(date, dateString) => handleFormChange({
                  target: {
                    name: "date",
                    value: dateString,
                  },
                })}
                format="YYYY-MM-DD"
                disabled={view}
              />
              <InputError isValid={isError.date} message="Date is required" />
            </Col>
          </Row>
          <Row className="mt-4 mb-2">
            <Col>
              <span className="edit-label">Remarks (Optional)</span>
              <Form.Control
                as="textarea"
                rows={3}
                name="remarks"
                value={formDetails.remarks}
                onChange={handleFormChange}
                disabled={view}
              />
            </Col>
          </Row>
          <Row className="mt-4 pt-3">
            <span className="edit-label mb-2">
              Journal Entries <span className="color-red">*</span>
            </span>
            <div className="edit-purchased-items">
              <Table>
                <thead className="edit-entries-items">
                  <tr>
                    <th className="color-gray" style={{ width: "25%" }}>Account Type</th>
                    <th className="color-gray" style={{ width: "13%" }}>Debit</th>
                    <th className="color-gray" style={{ width: "13%" }}>Credit</th>
                    <th className="color-gray" style={{ width: "18%" }}>Remarks</th>
                    <th className="color-gray" style={{ width: "30%" }}>Project Name</th>
                  </tr>
                </thead>
                <tbody>
                  {journalEntries.map((entry, index) => (
                    <tr key={index}>
                      <td>
                        <Select
                          options={accountTypes}
                          value={accountTypes.find((type) => type.value === entry.account_type) || {
                            value: entry.account_type,
                            label: entry.expense_type_name
                          }}
                          onChange={(selectedOption) => {
                            const updatedEntries = [...journalEntries];
                            updatedEntries[index].account_type = selectedOption.value;
                            setJournalEntries(updatedEntries);
                          }}
                          isDisabled={view}
                        />
                      </td>
                      <td>
                        <Form.Control
                          type="number"
                          name="debit"
                          value={entry.debit}
                          onChange={(e) => handleJournalEntryChange(e, index)}
                          onWheel={(e) => e.target.blur()}
                          disabled={view || entry.credit > 0}
                        />
                      </td>
                      <td>
                        <Form.Control
                          type="number"
                          name="credit"
                          value={entry.credit}
                          onChange={(e) => handleJournalEntryChange(e, index)}
                          onWheel={(e) => e.target.blur()}
                          disabled={view || entry.debit > 0}
                        />
                      </td>
                      <td>
                        <Form.Control
                          type="text"
                          name="remarks"
                          value={entry.remarks}
                          onChange={(e) => handleJournalEntryChange(e, index)}
                          disabled={view}
                        />
                      </td>
                      <td>
                        <Select
                          options={projects}
                          value={projects.find((p) => p.value === entry.project_id) || {
                            value: entry.project_id,
                            label: entry.project_name
                          }}
                          onChange={(selectedOption) => handleProjectChange(selectedOption, index)}
                          isDisabled={view}
                        />
                      </td>
                      <td className="text-middle">
                        {!view && (
                          <img
                            src={trash}
                            alt="delete"
                            onClick={() => deleteJournalEntryRow(index)}
                            style={{ cursor: "pointer" }}
                          />
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
              <InputError isValid={isError.journalEntries} message="Please add at least one journal entry" />
            </div>
          </Row>
          {!view && (
            <Row className="pt-3 PO-add-item">
              <Button type="button" onClick={addJournalEntryRow}>
                Add Entry
              </Button>
            </Row>
          )}
          <div className="d-flex justify-content-end mt-4 my-4">
            <div className="text-end" style={{ minWidth: '200px' }}>
              <div className="d-flex justify-content-between mb-2">
                <span className="print-table-footer-label grand-label d-flex justify-content-end">Total Debit:</span>
                <span className="print-table-footer-label grand-label">
                  PHP {numberFormat(totals.totalDebit)}
                </span>
              </div>
              <div className="d-flex justify-content-between">
                <span className="print-table-footer-label grand-label d-flex justify-content-end">Total Credit:</span>
                <span className="print-table-footer-label grand-label">
                  PHP {numberFormat(totals.totalCredit)}
                </span>
              </div>
            </div>
          </div>
          <div className="d-flex justify-content-end pt-4 pb-3">
            <button
              type="button"
              className="button-secondary me-3"
              onClick={() => navigate(-1)}
            >
              {view ? "Close" : "Cancel"}
            </button>
            {(add || edit) && (
              <button
                type="button"
                className="button-primary"
                onClick={handleSubmit}
                disabled={!isBalanced}
                title={!isBalanced ? "Total debits must equal total credits" : ""}
              >
                Submit
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

FormJournalEntry.defaultProps = {
  add: false,
  edit: false,
  view: false,
};

export default FormJournalEntry;