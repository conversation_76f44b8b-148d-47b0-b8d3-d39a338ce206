import React, { useState, useEffect } from "react";
import { Col, Form, Row, Tab, Tabs } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";
import Navbar from "../../Components/Navbar/Navbar";
import Table from "../../Components/TableTemplate/DataTable";
import ClickableRowTable from "../../Components/TableTemplate/OneTable";
import DeleteModal from "../../Components/Modals/DeleteModal";
import Select from "react-select";
import Moment from "moment";
import {
  getType,
  numberFormat,
  getTodayDateISO,
  selectDropdownStyle,
  TokenExpiry,
  toastStyle,
  formatDateNoTime,
} from "../../Helpers/Utils/Common";
import { CSVLink } from "react-csv";
import downloadIcon from "../../Assets/Images/download_icon.png";
import "./ProjectExpenses.css";
import "../Purchases/PurchaseOrders/PurchaseOrders.css";
import { searchProject } from "./../../Helpers/apiCalls/Manage/Projects";
import { searchPartner } from "../../Helpers/apiCalls/Manage/PartnerAPI";
import { getAllSuppliers } from "../../Helpers/apiCalls/suppliersApi";
import {
  searchProjectExpense,
  deleteProjectExpense,
} from "./../../Helpers/apiCalls/ProjectInvoice/ProjectExpenseApi";
import { getDistributor } from "../../Helpers/apiCalls/Distributor/DistributorApi";
import { isNamedImports } from "typescript";

export default function ProjectExpenses() {
  let navigate = useNavigate();
  const userType = getType();
  const [inactive, setInactive] = useState(true);
  const [tableData, setTableData] = useState([]);
  const [projects, setProjects] = useState([]);
  const [partners, setPartners] = useState([]);
  const [allDistributors, setAllDistributors] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [showLoader, setShowLoader] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const handleShowDeleteModal = () => setShowDeleteModal(true);
  const handleCloseDeleteModal = () => setShowDeleteModal(false);
  const [projectId, setProjectId] = useState("");
  const [selectedProject, setSelectedProject] = useState(null);
  const [selectedDistributor, setSelectedDistributor] = useState(null);
  const [selectedSupplier, setSelectedSupplier] = useState(null);

  const excelHeaders = [
    { label: "DATE", key: "project_expense_date" },
    { label: "PROJECT NAME", key: "name" },
    { label: "SUPPLIER", key: "supplier_name" },
    { label: "AMOUNT", key: "amount" },
    { label: "OTHER FEES", key: "other_fees" },
    { label: "GRAND TOTAL", key: "grand_total" },
    { label: "REMARKS", key: "remarks" },
    { label: "ADDED BY", key: "added_by_name" },
  ];

  /* FILTER CONFIGS */
  const [filterConfig, setFilterConfig] = useState({
    status: "pending",
    distributor_id: "",
    // franchise: "",
    // tabKey: "",
    // project_id: "",
    // franchised_on_from: "",
    // franchised_on_to: "",
  });

  const handleTabSelect = (tabKey) => {
    var newFilterConfig = {
      tab: tabKey,
      project_id: "",
      distributor_id: "",
      supplier_id: "",
    };

    setSelectedProject(null);
    setSelectedDistributor(null);
    setSelectedSupplier(null);

    switch (tabKey) {
      case "pending":
        setTableData([]);
        newFilterConfig.status = "pending";
        setFilterConfig(() => {
          return newFilterConfig;
        });
        break;
      case "approved":
        setTableData([]);
        newFilterConfig.status = "approved";
        setFilterConfig(() => {
          return newFilterConfig;
        });
        break;
      case "paid":
        setTableData([]);
        newFilterConfig.status = "paid";
        setFilterConfig(() => {
          return newFilterConfig;
        });
        break;
      // case "disapproved":
      //   setTableData([]);
      //   newFilterConfig.status = "disapproved";
      //   setFilterConfig(() => {
      //     return newFilterConfig;
      //   });
      //   break;
      case "all":
        setTableData([]);
        // newFilterConfig.status = "";
        setFilterConfig(() => {
          return newFilterConfig;
        });
        break;
      default:
        setTableData([]);
        break;
    }
  };

  async function fetchDistributors() {
    setAllDistributors([]);
    const response = await getDistributor();
    if (response.data) {
      const res = response.data.data.map((row) => {
        return {
          value: row.id,
          label: row.name,
        }
      })
      setAllDistributors([{value: '', label: 'All Distributors'}, ...res]);
    }
  }

  async function fetchProjects() {
    setShowLoader(true);
    setProjects([]);

    const response = await searchProject({});
    if (response.error) {
      if (response.error.data.status !== 404) {
        TokenExpiry(response.error);
      }
    } else {
      var projects = response.data.data.map((data) => {
        var info = {};
        info.label = data.name;
        info.value = data.id;
        return info;
      });
      setProjects([{ label: "All Projects", value: "" }, ...projects]);
    }

    setShowLoader(false);
  }

  async function fetchPartners() {
    setShowLoader(true);
    setPartners([]);

    const response = await searchPartner(null);
    if (response.error) {
      if (response.error.data.status !== 404) {
        TokenExpiry(response.error);
      }
    } else {
      var partners = response.data.data.map((data) => {
        var info = {};
        info.label = data.name;
        info.value = data.id;
        return info;
      });
      setPartners([{ label: "All Partners", value: "" }, ...partners]);
    }

    setShowLoader(false);
  }

  async function fetchAllSuppliers() {
    setSuppliers([]);
    const response = await getAllSuppliers();
    if (response.data) {
      const res = response.data.data.map((row) => {
        return {
          value: row.id,
          label: row.trade_name,
        };
      });
      setSuppliers(res);
    }
  }

  async function fetchData() {
    setShowLoader(true);
    setTableData([]);
    const response = await searchProjectExpense(filterConfig);
    if (response.data) {
      var allData = response.data.data.map((data) => {
        var info = data;
        info.added_on =
          data.added_on !== "0000-00-00" ? formatDateNoTime(data.added_on) : "";
        info.project_expense_date = formatDateNoTime(data.project_expense_date);
        info.amount = numberFormat(data.amount);
        info.other_fees = numberFormat(data.other_fees);
        info.grand_total = numberFormat(data.grand_total);
        info.balance = numberFormat(data.balance);
        info.paid_amount = data.paid_amount
          ? numberFormat(data.paid_amount)
          : "0.00";
        return info;
      });
      setTableData(allData);
    } else if (response.error) {
      if (response.error.data.status !== 404) {
        TokenExpiry(response.error);
      }
    }
    setShowLoader(false);
  }

  async function handleDeletePI() {
    const response = await deleteProjectExpense(projectId);

    if (response.data) {
      toast.success("Project Expense Deleted Successfully!", {
        style: toastStyle(),
      });
      setShowDeleteModal(false);
      fetchData();
      // setTimeout(() => refreshPage(), 1000);
    } else {
      toast.error("Error Deleting Project Expense", {
        style: toastStyle(),
      });
    }
  }

  // function handleSelectChange(e, id, franchisee_id, balance) {
  function handleSelectChange(e, row) {
    if (e.target.value === "edit-pi") {
      navigate("edit/" + row.id);
    } else if (e.target.value === "print-pi") {
      navigate("print/" + row.id);
    } else if (e.target.value === "delete-pi") {
      setProjectId(row.id);
      handleShowDeleteModal();
    } else if (e.target.value === "view") {
      navigate("view/" + row.id + "/" + row.status);
    }
  }

  function handleToCSV() {
    return (
      <CSVLink
        data={tableData}
        headers={excelHeaders}
        filename={`ProjectExpense_${Moment().format("YYYY-MM-DD")}.csv`}
        style={{ textDecoration: "none", color: "#ffffff" }}
      >
        Export to CSV
      </CSVLink>
    );
  }
  {/* <Row>
    <Col className="d-flex justify-content-end mb-3">
      <div className="justify-content-center align-items-center ">
        <CSVLink
          className="button-primary px-3 py-3 justify-content-center align-items-center download-csv"
          data={tableData}
          headers={excelHeaders}
          target="_blank"
          filename={`${getTodayDateISO()} Project Expense`}
        >
          <span className="me-2">
            <img width={20} height={20} src={downloadIcon} alt=""></img>
          </span>
          Download Excel
        </CSVLink>
      </div>
    </Col>
  </Row> */}

  function ActionBtn(row, type) {
    return (
      <Form.Select
        name="action"
        className="PO-select-action"
        onChange={(e) => handleSelectChange(e, row)}
      >
        <option value="" hidden selected>
          Select
        </option>
        {(userType === "admin" || userType === "finance_accounting_officer")  && (
          <>
            <option value="view" className="color-options">
              View
            </option>
            {(row?.status !== "approved" && row?.status !== "paid") &&(
              <>
                <option value="edit-pi" className="color-options">
                  Edit
                </option>
              </>
            )}
            {(row.status === "pending") && (
              <>
                <option value="delete-pi" className="color-red">
                  Delete
                </option>
              </>
            )}
          </>
        )}
      </Form.Select>
    );
  }

  function handleRowClick(row) {
    if (row && row.id) {
      window.open(`projectexpense/view/${row.id}/${row.status}`, "_blank");
    }
  }

  // function ViewBtn(row) {
  //   return (
  //     <button
  //       type="button"
  //       className="btn btn-sm view-btn-table"
  //       onClick={(e) => navigate("view/" + row.id + "/" + row.status)}
  //     >
  //       View
  //     </button>
  //   );
  // }

  useEffect(() => {
    fetchProjects();
    fetchPartners();
    fetchAllSuppliers();
    fetchDistributors();
  }, []);

  useEffect(() => {
    fetchData();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterConfig]);

  return (
    <div>
      <div className="page">
        <Navbar
          onCollapse={(inactive) => {
            setInactive(inactive);
          }}
          active={"EXPENSE"}
        />
      </div>

      <div className={`manager-container ${inactive ? "inactive" : "active"}`}>
        {/* headers */}
        <Row className="mb-4">
          <Col xs={6}>
            <h1 className="page-title">PROJECT EXPENSE</h1>
          </Col>
          <Col xs={6} className="d-flex justify-content-end">
          <button className="add-btn text-center">{handleToCSV()}</button>

            <button
              className="add-btn"
              onClick={() => navigate("/projectexpense/add")}
            >
              Add
            </button>
          </Col>
        </Row>
        

        <Tabs
          activeKey={filterConfig.status}
          defaultActiveKey={filterConfig.status}
          id="PO-tabs"
          onSelect={handleTabSelect}
        >
          <Tab eventKey="pending" title="FOR APPROVAL">
            {/* filters */}
            <div className="my-2 ms-2 PO-filters PI-filters d-flex">

              <Select
                className="dropsearch-filter px-0 py-0 me-2"
                classNamePrefix="react-select"
                placeholder="Select Project"
                styles={selectDropdownStyle}
                value={selectedProject}
                options={projects}
                onChange={(e) => {
                  setSelectedProject(e);
                  setFilterConfig((prev) => (
                    { ...prev, project_id: e.value }
                  ));
                }}
              />

              {/* <Select
                className="dropsearch-filter px-0 py-0 me-2"
                classNamePrefix="react-select"
                placeholder="Select Distributor"
                styles={selectDropdownStyle}
                value={selectedDistributor}
                options={allDistributors}
                onChange={(e) => {
                  setSelectedDistributor(e);
                  setFilterConfig((prev) => (
                    { ...prev, distributor_id: e.value }
                  ));
                }}
              /> */}
              <Select
                className="dropsearch-filter px-0 py-0 me-2"
                classNamePrefix="react-select"
                placeholder="Select Supplier"
                styles={selectDropdownStyle}
                value={selectedSupplier}
                options={suppliers}
                onChange={(e) => {
                  setSelectedSupplier(e);
                  setFilterConfig((prev) => (
                    { ...prev, supplier_id: e.value }
                  ));
                }}
              />
            </div>
            {/* content */}
            <div className="">
              <Table
                tableHeaders={[
                  // "-",
                  "DATE",
                  "PROJECT NAME",
                  "SUPPLIER",
                  "AMOUNT",
                  "OTHER FEES",
                  "GRAND TOTAL",
                  "REMARKS",
                  "ADDED BY",
                  "ACTIONS",
                ]}
                headerSelector={[
                  // "-",
                  "project_expense_date",
                  "name",
                  "supplier_name",
                  "amount",
                  "other_fees",
                  "grand_total",
                  "remarks",
                  "added_by_name",
                ]}
                tableData={tableData}
                // ViewBtn={(row) => ViewBtn(row)}
                ActionBtn={(row) => ActionBtn(row, "pending")}
                showLoader={showLoader}
                withActionData={false}
              />
            </div>
            <div className="mb-2" />
          </Tab>
          <Tab eventKey="approved" title="UNPAID">
            {/* filters */}
            <div className="my-2 ms-2 PO-filters PI-filters d-flex">
              <Select
                className="dropsearch-filter px-0 py-0 me-2"
                classNamePrefix="react-select"
                placeholder="Select Project"
                styles={selectDropdownStyle}
                value={selectedProject}
                options={projects}
                onChange={(e) => {
                  setSelectedProject(e);
                  setFilterConfig((prev) => {
                    return { ...prev, project_id: e.value };
                  });
                }}
              />

              {/* <Select
                className="dropsearch-filter px-0 py-0 me-2"
                classNamePrefix="react-select"
                placeholder="Select Distributor"
                styles={selectDropdownStyle}
                value={selectedDistributor}
                options={allDistributors}
                onChange={(e) => {
                  setSelectedDistributor(e);
                  setFilterConfig((prev) => {
                    return { ...prev, distributor_id: e.value };
                  });
                }}
              /> */}
              <Select
                className="dropsearch-filter px-0 py-0 me-2"
                classNamePrefix="react-select"
                placeholder="Select Supplier"
                styles={selectDropdownStyle}
                value={selectedSupplier}
                options={suppliers}
                onChange={(e) => {
                  setSelectedSupplier(e);
                  setFilterConfig((prev) => {
                    return { ...prev, supplier_id: e.value };
                  });
                }}
              />
            </div>

            {/* content */}
            <div className="">
              <Table
                tableHeaders={[
                  // "-",
                  "DATE",
                  "PROJECT NAME",
                  "SUPPLIER",
                  "AMOUNT",
                  "OTHER FEES",
                  "GRAND TOTAL",
                  "REMARKS",
                  "ADDED BY",
                  "ACTIONS",
                ]}
                headerSelector={[
                  // "-",
                  "project_expense_date",
                  "name",
                  "supplier_name",
                  "amount",
                  "other_fees",
                  "grand_total",
                  "remarks",
                  "added_by_name",
                ]}
                tableData={tableData}
                // ViewBtn={(row) => ViewBtn(row)}
                ActionBtn={(row) => ActionBtn(row, "approved")}
                showLoader={showLoader}
                withActionData={false}
              />
            </div>
            <div className="mb-2" />
          </Tab>
          <Tab eventKey="paid" title="PAID">
            {/* filters */}
            <div className="my-2 ms-2 PO-filters PI-filters d-flex">
              <Select
                className="dropsearch-filter px-0 py-0 me-2"
                classNamePrefix="react-select"
                placeholder="Select Project"
                styles={selectDropdownStyle}
                value={selectedProject}
                options={projects}
                onChange={(e) => {
                  setSelectedProject(e);
                  setFilterConfig((prev) => {
                    return { ...prev, project_id: e.value };
                  });
                }}
              />

              {/* <Select
                className="dropsearch-filter px-0 py-0 me-2"
                classNamePrefix="react-select"
                placeholder="Select Distributor"
                styles={selectDropdownStyle}
                value={selectedDistributor}
                options={allDistributors}
                onChange={(e) => {
                  setSelectedDistributor(e);
                  setFilterConfig((prev) => {
                    return { ...prev, distributor_id: e.value };
                  });
                }}
              /> */}
              <Select
                className="dropsearch-filter px-0 py-0 me-2"
                classNamePrefix="react-select"
                placeholder="Select Supplier"
                styles={selectDropdownStyle}
                value={selectedSupplier}
                options={suppliers}
                onChange={(e) => {
                  setSelectedSupplier(e);
                  setFilterConfig((prev) => {
                    return { ...prev, supplier_id: e.value };
                  });
                }}
              />
            </div>

            {/* content */}
            <div className="sales-tbl">
              <ClickableRowTable
                tableHeaders={[
                  // "-",
                  "DATE",
                  "PROJECT NAME",
                  "SUPPLIER",
                  "AMOUNT",
                  "OTHER FEES",
                  "GRAND TOTAL",
                  "REMARKS",
                  "ADDED BY",
                ]}
                headerSelector={[
                  // "-",
                  "project_expense_date",
                  "name",
                  "supplier_name",
                  "amount",
                  "other_fees",
                  "grand_total",
                  "remarks",
                  "added_by_name",
                ]}
                tableData={tableData}
                rowClickHandler={handleRowClick} 
                showLoader={showLoader}
              />
            </div>
            <div className="mb-2" />
          </Tab>
          {/* <Tab eventKey="disapproved" title="DISAPPROVED">
            <div className="my-2 ms-2 PO-filters PI-filters d-flex">
              <Select
                className="dropsearch-filter px-0 py-0 me-2"
                classNamePrefix="react-select"
                placeholder="Select Project"
                styles={selectDropdownStyle}
                value={selectedProject}
                options={projects}
                onChange={(e) => {
                  setSelectedProject(e);
                  setFilterConfig((prev) => {
                    return { ...prev, project_id: e.value };
                  });
                }}
              />

              <Select
                className="dropsearch-filter px-0 py-0 me-2"
                classNamePrefix="react-select"
                placeholder="Select Distributor"
                styles={selectDropdownStyle}
                value={selectedDistributor}
                options={allDistributors}
                onChange={(e) => {
                  setSelectedDistributor(e);
                  setFilterConfig((prev) => {
                    return { ...prev, distributor_id: e.value };
                  });
                }}
              />
              <Select
                className="dropsearch-filter px-0 py-0 me-2"
                classNamePrefix="react-select"
                placeholder="Select Supplier"
                styles={selectDropdownStyle}
                value={selectedSupplier}
                options={suppliers}
                onChange={(e) => {
                  setSelectedSupplier(e);
                  setFilterConfig((prev) => {
                    return { ...prev, supplier_id: e.value };
                  });
                }}
              />
            </div>

            <div className="">
              <Table
                tableHeaders={[
                  "DATE",
                  "PROJECT NAME",
                  "SUPPLIER",
                  "AMOUNT",
                  "OTHER FESS",
                  "GRAND TOTAL",
                  "REMARKS",
                  "ADDED BY",
                  "ACTIONS",
                ]}
                headerSelector={[
                  "project_expense_date",
                  "name",
                  "supplier_name",
                  "amount",
                  "other_fees",
                  "grand_total",
                  "remarks",
                  "added_by_name",
                ]}
                tableData={tableData}
                ActionBtn={(row) => ActionBtn(row, "disapproved")}
                showLoader={showLoader}
                withActionData={false}
              />
            </div>
            <div className="mb-2" />
          </Tab> */}
          <Tab eventKey="all" title="ALL">
            {/* filters */}
            <div className="my-2 ms-2 PO-filters PI-filters d-flex">
              <Select
                className="dropsearch-filter px-0 py-0 me-2"
                classNamePrefix="react-select"
                placeholder="Select Project"
                styles={selectDropdownStyle}
                value={selectedProject}
                options={projects}
                onChange={(e) => {
                  setSelectedProject(e);
                  setFilterConfig((prev) => {
                    return { ...prev, project_id: e.value };
                  });
                }}
              />

              {/* <Select
                className="dropsearch-filter px-0 py-0 me-2"
                classNamePrefix="react-select"
                placeholder="Select Distributor"
                styles={selectDropdownStyle}
                value={selectedDistributor}
                options={allDistributors}
                onChange={(e) => {
                  setSelectedDistributor(e);
                  setFilterConfig((prev) => {
                    return { ...prev, distributor_id: e.value };
                  });
                }}
              /> */}
              <Select
                className="dropsearch-filter px-0 py-0 me-2"
                classNamePrefix="react-select"
                placeholder="Select Supplier"
                styles={selectDropdownStyle}
                value={selectedSupplier}
                options={suppliers}
                onChange={(e) => {
                  setSelectedSupplier(e);
                  setFilterConfig((prev) => {
                    return { ...prev, supplier_id: e.value };
                  });
                }}
              />
            </div>

            {/* content */}
            <div className="">
              <Table
                tableHeaders={[
                  // "-",
                  "DATE",
                  "PROJECT NAME",
                  "SUPPLIER",
                  "AMOUNT",
                  "OTHER FEES",
                  "GRAND TOTAL",
                  "REMARKS",
                  "STATUS",
                  "ADDED BY",
                  "ACTIONS",
                ]}
                headerSelector={[
                  // "-",
                  "project_expense_date",
                  "name",
                  "supplier_name",
                  "amount",
                  "other_fees",
                  "grand_total",
                  "remarks",
                  "status",
                  "added_by_name",
                ]}
                tableData={tableData}
                // ViewBtn={(row) => ViewBtn(row)}
                ActionBtn={(row) => ActionBtn(row, "all")}
                showLoader={showLoader}
                withActionData={false}
              />
            </div>
            <div className="mb-2" />
          </Tab>
        </Tabs>
        <div className="mb-2" />
      </div>
      <DeleteModal
        show={showDeleteModal}
        onHide={() => handleCloseDeleteModal()}
        text="project expense"
        onDelete={() => handleDeletePI()}
      />
    </div>
  );
}
