import React from "react";
import jsPD<PERSON> from "jspdf";
import "jspdf-autotable";

export default function ExportPDF({ name, data, header }) {
  function print() {
    const doc = new jsPDF("l", "mm", "tabloid");

    // Extract titles for table header
    const tableHead = [header.map((h) => h.title)];

    // Extract values for table body using keys
    const tableBody = data.map((item) =>
      header.map((h) => item[h.key] ?? "")
    );

    doc.text(name, 20, 20);

    doc.autoTable({
      head: tableHead,
      body: tableBody,
      theme: "plain",
      styles: {
        fontSize: 7,
      },
      margin: { top: 25 },
    });

    doc.save(`${name}.pdf`);
  }

  return (
    <button className="add-btn text-center" onClick={print}>
      Export to PDF
    </button>
  );
}