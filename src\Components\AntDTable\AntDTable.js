import { SearchOutlined } from "@ant-design/icons";
import React, { useRef, useState } from "react";
import Highlight<PERSON> from "react-highlight-words";
import { Button, Input, Space, Table } from "antd";
import { useNavigate } from "react-router-dom";

const AntDTable = ({
  tableHeaders,
  headerSelector,
  tableData,
  showLoader,
  rowClickPath,
  isClickable,
  ActionBtn,
  withAction,
  withExpense,
  expenseBtn,
  loading,
  customFooter,
  className,
  searchExcluded = [],
}) => {
  const navigate = useNavigate();
  const [searchText, setSearchText] = useState("");
  const [searchedColumn, setSearchedColumn] = useState("");
  const searchInput = useRef(null);

  const paginationConfig = {
    pageSizeOptions: ["10", "20", "50", "100"],
    showSizeChanger: true,
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
  };

  const handleSearch = (selectedKeys, confirm, dataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  };

  const handleReset = (clearFilters) => {
    clearFilters();
    setSearchText("");
  };

  const handleRowClick = (record) => {
    if (isClickable) {
      const id = record.id; // Assuming you have an 'id' property in your data
      navigate(`${rowClickPath}/${id}`);
    }
  };

    const getColumnSearchProps = (dataIndex) => {
    if (!searchExcluded.includes(dataIndex)) {
      return {
        filterDropdown: ({
          setSelectedKeys,
          selectedKeys,
          confirm,
          clearFilters,
          close,
        }) => (
          <div
            style={{
              padding: 8,
            }}
            onKeyDown={(e) => e.stopPropagation()}
          >
            <Input
              ref={searchInput}
              placeholder={`Search ${dataIndex}`}
              value={selectedKeys[0]}
              onChange={(e) =>
                setSelectedKeys(e.target.value ? [e.target.value] : [])
              }
              onPressEnter={() =>
                handleSearch(selectedKeys, confirm, dataIndex)
              }
              style={{
                marginBottom: 8,
                display: "block",
              }}
            />
            <Space>
              <Button
                type="primary"
                onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                icon={<SearchOutlined />}
                size="small"
                style={{
                  width: 90,
                }}
              >
                Search
              </Button>
              <Button
                onClick={() => clearFilters && handleReset(clearFilters)}
                size="small"
                style={{
                  width: 90,
                }}
              >
                Reset
              </Button>
              <Button
                type="link"
                size="small"
                onClick={() => {
                  confirm({
                    closeDropdown: false,
                  });
                  setSearchText(selectedKeys[0]);
                  setSearchedColumn(dataIndex);
                }}
              >
                Filter
              </Button>
              <Button
                type="link"
                size="small"
                onClick={() => {
                  close();
                }}
              >
                Close
              </Button>
            </Space>
          </div>
        ),
        filterIcon: (filtered) => (
          <SearchOutlined
            style={{
              color: filtered ? "var(--sidebar-color)" : undefined,
            }}
          />
        ),
        onFilter: (value, record) =>
          record[dataIndex]
            ?.toString()
            .toLowerCase()
            .includes(value.toLowerCase()),
        onFilterDropdownOpenChange: (visible) => {
          if (visible) {
            setTimeout(() => searchInput.current?.select(), 100);
          }
        },
        render: (text) =>
          searchedColumn === dataIndex ? (
            <Highlighter
              highlightStyle={{
                backgroundColor: "var(--sidebar-color)",
                padding: 0,
              }}
              searchWords={[searchText]}
              autoEscape
              textToHighlight={text ? text.toString() : ""}
            />
          ) : (
            text
          ),
      };
    }
  };

  const columns = tableHeaders.map((header, index) => {
    return {
      title: header,
      dataIndex: headerSelector[index],
      key: headerSelector[index],
      ...getColumnSearchProps(headerSelector[index]),
      sorter: (a, b) => {
        const valueA = a[headerSelector[index]];
        const valueB = b[headerSelector[index]];

        if (typeof valueA === "string" && typeof valueB === "string") {
          return valueA.localeCompare(valueB, undefined, {
            sensitivity: "base",
          });
        } else if (typeof valueA === "number" && typeof valueB === "number") {
          return valueA - valueB;
        } else if (valueA instanceof Date && valueB instanceof Date) {
          return valueA - valueB;
        } else {
          // One value is a string and the other is a number or date, prioritize string sorting
          return typeof valueA === "string" ? -1 : 1;
        }
      },
      sortDirections: ["descend", "ascend"],
    };
  });

  if (withAction) {
    columns.push({
      title: "ACTION",
      key: "action",
      width: 250,
      color: "var(--sidebar-color)",
      render: (_, record) => <ActionBtn row={record} />,
    });
  }

  return (
    <Table
      columns={columns}
      dataSource={tableData}
      onRow={(record) => ({
        onClick: () => handleRowClick(record),
      })}
      loading={loading}
      pagination={paginationConfig}
      footer={customFooter}
      size="small"
      className="table-bg"
    />
  );
};

export default AntDTable;
