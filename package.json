{"name": "myt_erp", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/fontawesome-svg-core": "^1.3.0", "@fortawesome/free-solid-svg-icons": "^6.0.0", "@fortawesome/react-fontawesome": "^0.1.17", "@material-ui/lab": "^4.0.0-alpha.61", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf/renderer": "^2.3.0", "@restart/hooks": "^0.4.7", "@testing-library/jest-dom": "^5.16.2", "@testing-library/react": "^12.1.4", "@testing-library/user-event": "^13.5.0", "@types/jest": "^28.1.3", "@types/node": "^18.0.0", "@types/react": "^18.0.14", "@types/react-dom": "^18.0.5", "antd": "^5.14.0", "axios": "^1.6.7", "bootstrap": "^5.1.3", "chart.js": "^4.4.9", "compressorjs": "^1.2.1", "css-loader": "^6.10.0", "date-fns": "^2.29.3", "dayjs": "^1.11.10", "formik": "^2.2.9", "html-webpack-plugin": "^5.6.0", "html2pdf": "^0.0.11", "html2pdf.js": "^0.10.2", "jspdf": "^2.5.1", "jspdf-autotable": "^3.5.25", "moment": "^2.29.3", "print-js": "^1.6.0", "react": "^17.0.2", "react-autocomplete-input": "^1.0.18", "react-bootstrap": "^2.2.1", "react-bootstrap-typeahead": "^6.0.0", "react-chartjs-2": "^5.2.0", "react-csv": "^2.2.2", "react-csv-downloader": "^2.7.1", "react-data-table-component": "^7.5.2", "react-data-table-component-footer": "^6.12.0-beta.2", "react-datepicker": "^4.8.0", "react-datetime-picker": "^3.5.0", "react-dom": "^17.0.2", "react-form-data": "^0.2.4", "react-highlight-words": "^0.20.0", "react-hot-toast": "^2.3.0", "react-loading": "^2.0.3", "react-moment": "^1.1.2", "react-multi-select-component": "^4.2.3", "react-responsive-carousel": "^3.2.23", "react-router-dom": "^6.2.2", "react-scripts": "^5.0.1", "react-select": "^5.4.0", "react-select-search": "^4.1.0", "react-spinners": "^0.13.4", "react-to-pdf": "^0.0.14", "react-to-print": "^2.14.11", "react-toastify": "^8.2.0", "sass": "^1.54.4", "source-map-loader": "^5.0.0", "style-loader": "^3.3.4", "styled-components": "^5.3.5", "typescript": "^4.7.4", "web-vitals": "^2.1.4", "xlsx": "^0.18.5", "yarn": "^1.22.19", "yup": "^0.32.11"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11"}}