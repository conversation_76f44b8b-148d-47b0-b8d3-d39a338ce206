import { Sync<PERSON>oader } from "react-spinners";
import DataTable from "react-data-table-component";
import NoDataPrompt from "../NoDataPrompt/NoDataPrompt";
import "./Table.css";

export default function Table({
    tableHeaders,
    headerSelector,
    tableData,
    ViewButton,
    showLoader,
}) {
    const columns = tableHeaders.map((header, index) => {
        if (header.includes("PYMT DATE")) {
            return {
                name: "PYMT\nDATE",
                selector: (row) => row[headerSelector[index]],
                center: true,
                sortable: true,
                width: "120px",
                minWidth: "120px",
                maxWidth: "170px",
                reorder: true,
                wrap: true,
            };
        } else if (header.includes("PYMT MODE")) {
            return {
                name: "PYMT\nMODE",
                selector: (row) => row[headerSelector[index]],
                center: true,
                sortable: true,
                width: "150px",
                minWidth: "150px",
                maxWidth: "200px",
                reorder: true,
                wrap: true,
            };
        } else if (header.includes("PO DATE")) {
            return {
                name: header,
                selector: (row) => row[headerSelector[index]],
                center: true,
                sortable: true,
                width: "70px",
                minWidth: "30px",
                maxWidth: "75px",
                reorder: true,
            };
        } else if (header.includes("PE DATE")) {
            return {
                name: header,
                selector: (row) => row[headerSelector[index]],
                center: true,
                sortable: true,
                width: "70px",
                minWidth: "30px",
                maxWidth: "75px",
                reorder: true,
            };
        } else if (header === "PO NO") {
            return {
                name: header,
                selector: (row) => row[headerSelector[index]],
                button: true,
                cell: ViewButton,
                width: "8rem",
                left: true,
                reorder: false,
                wordWrap: "break-word",
            };
        } else if (header === "PE NO") {
            return {
                name: header,
                selector: (row) => row[headerSelector[index]],
                cell: ViewButton,
                width: "8rem",
                left: true,
                reorder: false,
                wordWrap: "break-word",
            };
        } else if (header === "AMT") {
            return {
                name: header,
                right: true,
                selector: (row) => row[headerSelector[index]],
                width: "2vw",
                reorder: true,
                sortable: true,
                wrap: true,
            };
        } else if (header.includes("SUPPLIER")) {
            return {
                name: header,
                selector: (row) => row[headerSelector[index]],
                left: true,
                sortable: true,
                width: "105px",
                minWidth: "105px",
                maxWidth: "155px",
                reorder: true,
                wrap: true,
            };
        } else {
            return {
                name: header,
                selector: (row) => row[headerSelector[index]],
                left: true,
                sortable: true,
                width: "10vw",
                reorder: true,
            };
        } 
    });

    const paginationComponentOptions = {
        rowsPerPageText: "",
        noRowsPerPage: true,
    };

    const customStyles = {
        rows: {
            style: {
                minHeight: "5.2vh",
            },
        },
        headCells: {
            style: {
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                paddingLeft: "8px",
                paddingRight: "8px",
                minHeight: "7vh",
                whiteSpace: "pre-line", // This allows line breaks in header text
            },
        },
        cells: {
            style: {
                paddingLeft: "8px",
                paddingRight: "8px",
            },
        },
    };

    return showLoader ? (
        <div className="d-flex justify-content-center my-5">
            <SyncLoader color="#5ac8e1" size={15} />
        </div>
    ) : (
        <DataTable
            pagination
            striped
            fixedHeader
            fixedHeaderScrollHeight="80vh"
            columns={columns}
            data={tableData}
            customStyles={customStyles}
            paginationComponentOptions={paginationComponentOptions}
            noDataComponent={<NoDataPrompt />}
        />
    );
}
