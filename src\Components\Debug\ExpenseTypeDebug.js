import React, { useState } from 'react';
import { getAllExpenseType, debugGetAllExpenseType } from '../../Helpers/apiCalls/expensetypesApi';
import { Button, Card, Alert } from 'react-bootstrap';

const ExpenseTypeDebug = () => {
  const [response, setResponse] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const testAPI = async () => {
    setLoading(true);
    setError(null);
    setResponse(null);

    try {
      console.log("🧪 Testing Expense Types API...");
      
      // Test the debug version first
      const debugResponse = await debugGetAllExpenseType();
      console.log("🧪 Debug Response:", debugResponse);
      
      // Test the actual function
      const actualResponse = await getAllExpenseType();
      console.log("🧪 Actual Response:", actualResponse);
      
      setResponse({
        debug: debugResponse,
        actual: actualResponse
      });
      
    } catch (err) {
      console.error("🧪 Test Error:", err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-4">
      <Card>
        <Card.Header>
          <h4>🧪 Expense Types API Debug Tool</h4>
        </Card.Header>
        <Card.Body>
          <Button 
            variant="primary" 
            onClick={testAPI} 
            disabled={loading}
          >
            {loading ? "Testing..." : "Test API"}
          </Button>
          
          {error && (
            <Alert variant="danger" className="mt-3">
              <strong>Error:</strong> {error}
            </Alert>
          )}
          
          {response && (
            <div className="mt-3">
              <h5>📊 API Response Results:</h5>
              
              <div className="mb-3">
                <h6>🔍 Debug Response:</h6>
                <pre style={{ 
                  background: '#f8f9fa', 
                  padding: '10px', 
                  borderRadius: '4px',
                  fontSize: '12px',
                  maxHeight: '300px',
                  overflow: 'auto'
                }}>
                  {JSON.stringify(response.debug, null, 2)}
                </pre>
              </div>
              
              <div className="mb-3">
                <h6>✅ Processed Response:</h6>
                <pre style={{ 
                  background: '#f8f9fa', 
                  padding: '10px', 
                  borderRadius: '4px',
                  fontSize: '12px',
                  maxHeight: '300px',
                  overflow: 'auto'
                }}>
                  {JSON.stringify(response.actual, null, 2)}
                </pre>
              </div>
              
              {response.actual?.data && Array.isArray(response.actual.data) && (
                <Alert variant="success">
                  ✅ Success! Found {response.actual.data.length} expense types
                </Alert>
              )}
              
              {response.actual?.error && (
                <Alert variant="warning">
                  ⚠️ API returned error: {response.actual.error}
                </Alert>
              )}
            </div>
          )}
        </Card.Body>
      </Card>
    </div>
  );
};

export default ExpenseTypeDebug;
